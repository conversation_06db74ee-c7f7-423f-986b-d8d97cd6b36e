package com.zeek.core.common.constants;

import com.google.common.collect.Lists;
import com.zeek.core.common.statemachine.BaseEvent;
import com.zeek.core.common.statemachine.BaseStatus;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.math.BigInteger;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: xiang.bai
 * @description:
 * @create: 2024-01-31
 **/
public interface ZeekConstants {

    String SCORE = "score";
    String AMOUNT = "amount";
    String SOCIALFI = "socialfi_";
    String MEMECULTURE = "memeCulture_";

    String HEX_PREFIX = "0x";
    /**
     * 0地址
     */
    String ZEROADDRESS = "0x0000000000000000000000000000000000000000";
    String WBERA_ADDRESS = "0x6969696969696969696969696969696969696969";
    String BERA_SYMBOL = "BERA";
    String QUEST_VERIFY = "questVerify";
    String SIGNER = "Signer";

    String QUEST_VERIFY_SIGN = "quest:verify:sign:";
    @Getter
    enum MarketStatus implements BaseStatus {
        // 初始创建状态，等待上链确认
        Pending,
        // 市场运行中，可以接受预测,
        Running,
        // 市场结束
        Ended,
        // 市场关闭
        Closed;


        public static MarketStatus get(String name) {
            for (MarketStatus status : MarketStatus.values()) {
                if (status.name().equals(name)) {
                    return status;
                }
            }
            return null;
        }

        public static List<String> visibleMarketStatus() {
            return Lists.newArrayList(Pending, Running, Ended)
                .stream().map(Enum::name).collect(Collectors.toList());
        }
    };

    enum UserStatus {
        init,
        normal
    }

    enum UserRole {
        GENESIS,
        NORMAL,
        HNWI,
        ;

        public static UserRole get(String name) {
            for (UserRole status : UserRole.values()) {
                if (status.name().equals(name)) {
                    return status;
                }
            }
            return null;
        }
    }

    // code 使用的场景
    enum CodeScenes {
        DEFAULT_INVITE,

        MINT_GENESIS_NFT,
        MINT_BOX,
        ;

        public static CodeScenes get(String name) {
            for (CodeScenes status : CodeScenes.values()) {
                if (status.name().equals(name)) {
                    return status;
                }
            }
            return null;
        }
    }

    @Getter
    enum WishStatus implements BaseStatus {
        Pending,
        Active,
        Expired,
        Closed,
        Finished;

        public static WishStatus get(String name) {
            for (WishStatus status : WishStatus.values()) {
                if (status.name().equals(name)) {
                    return status;
                }
            }
            return null;
        }

        public static List<String> visibleWishStatus() {
            return Lists.newArrayList(Active, Finished, Expired, Closed).stream().map(Enum::name).collect(Collectors.toList());
        }

    }

    @Getter
    enum QuestStatus implements BaseStatus {
        Initial,
        Pending,
        Active,
        Finished,
        Closed;

        public static QuestStatus get(String name) {
            for (QuestStatus status : QuestStatus.values()) {
                if (status.name().equals(name)) {
                    return status;
                }
            }
            return null;
        }

        public static List<String> visibleQuestStatus() {
            return Lists.newArrayList(Active.name(), Finished.name(), Closed.name());
        }
    }

    enum QuestEvent implements BaseEvent {
        OSP_CREATE_QUEST_ACTIVITY_ON_CHAIN,
        ZEEK_ISSUED_QUEST_ON_CHAIN,
        ZEEK_FINISH_QUEST_ON_CHAIN,
        ZEEK_REFUND_QUEST_ON_CHAIN

    }

    enum WishEvent implements BaseEvent {
        OSP_CREATE_ACTIVITY_ON_CHAIN,
        ZEEK_ISSUED_WISH_ON_CHAIN,
        ZEEK_OFFERED_WISH_ON_CHAIN,
        ZEEK_EXPIRED_WISH,
        ZEEK_REFUND_WISH_ON_CHAIN,
    }

    enum MarketEvent implements BaseEvent {
        ZEEK_CREATE_MARKET,             // 创建市场
        ZEEK_ISSUED_MARKET_ON_CHAIN,    // 市场上链确认
        ZEEK_EXPIRED_MARKET,             // 时间到期，市场结束
        ZEEK_CLOSED_MARKET,             // 完成市场，链上结果已分配
    }

    @Getter
    enum QuestType {
        FollowTwitter(0),
        ;

        private final int code;

        QuestType(int code) {
            this.code = code;
        }

        public static QuestType get(String name) {
            for (QuestType type : QuestType.values()) {
                if (type.name().equals(name)) {
                    return type;
                }
            }
            return null;
        }
    }

    enum WishType {
        PublicQuestion(0),
        PrivateQuestion(1),
        Referral(2),
        ;

        private final int code;

        WishType(int code) {
            this.code = code;
        }

        public static WishType get(String name) {
            for (WishType type : WishType.values()) {
                if (type.name().equals(name)) {
                    return type;
                }
            }
            return null;
        }

        public static WishType get(int code, Boolean restricted) {
            switch (code) {
                case 0 -> {
                    return restricted ? WishType.PrivateQuestion : WishType.PublicQuestion;
                }
                case 1 -> {
                    return Referral;
                }
            }
            return null;
        }
    }

    @Getter
    enum TransactionRecordStatus {
        CLAIMED(0),
        UNCLAIM(1),

        ;
        private final int claimedStatus;

        TransactionRecordStatus(int claimedStatus) {
            this.claimedStatus = claimedStatus;
        }

        public static TransactionRecordStatus get(int claimedStatus) {
            for (TransactionRecordStatus status : TransactionRecordStatus.values()) {
                if (status.claimedStatus == claimedStatus) {
                    return status;
                }
            }
            return null;
        }
    }

    // 来自合约
    enum QuestParticipant {
        OWNER(0),
        PARTICIPANT_CLAIMED(1),
        PARTICIPANT_UNCLAIM(2),

        ;
        private final int code;

        QuestParticipant(int code) {
            this.code = code;
        }

        public static QuestParticipant get(int code) {
            for (QuestParticipant participant : QuestParticipant.values()) {
                if (participant.code == code) {
                    return participant;
                }
            }
            return null;
        }
    }

    // 来自合约
    enum WishParticipant {
        Issuer(0),
        Owner(1),
        Talent(2),
        ;
        private final int code;

        WishParticipant(int code) {
            this.code = code;
        }

        public static WishParticipant get(int code) {
            for (WishParticipant participant : WishParticipant.values()) {
                if (participant.code == code) {
                    return participant;
                }
            }
            return null;
        }
    }

    // 来自合约
    enum WishScene {
        Unlock(0),
        Bid(1),

        Quest(2),
        ;
        private final int code;

        WishScene(int code) {
            this.code = code;
        }

        public static WishScene get(int code) {
            for (WishScene scene : WishScene.values()) {
                if (scene.code == code) {
                    return scene;
                }
            }
            return null;
        }
    }

    enum TransactionRecordType {
        WishBidded_Talent,
        WishUnlocked_Issuer,
        WishUnlocked_Owner,
        WishUnlocked_Talent,
        QuestVerify_Talent,
        ;

        public static TransactionRecordType get(String name) {
            for (TransactionRecordType type : TransactionRecordType.values()) {
                if (type.name().equals(name)) {
                    return type;
                }
            }
            return null;
        }

        public static List<TransactionRecordType> WISH_UNLOCK_TYPE() {
            return Lists.newArrayList(WishUnlocked_Issuer, WishUnlocked_Owner, WishUnlocked_Talent);
        }

        public static TransactionRecordType get(BigInteger scene, BigInteger role) {
            WishScene wishScene = WishScene.get(scene.intValue());
            WishParticipant wishParticipant = WishParticipant.get(role.intValue());
            switch (wishScene) {
                case Unlock -> {
                    switch (wishParticipant) {
                        case Issuer -> {
                            return WishUnlocked_Issuer;
                        }
                        case Owner -> {
                            return WishUnlocked_Owner;
                        }
                        case Talent -> {
                            return WishUnlocked_Talent;
                        }
                    }
                }
                case Bid -> {
                    switch (wishParticipant) {
                        case Talent -> {
                            return WishBidded_Talent;
                        }
                    }
                }
                case Quest -> {
                    switch (wishParticipant) {
                        case Talent -> {
                            return QuestVerify_Talent;
                        }
                    }
                }
            }
            return null;
        }

    }

    enum WishRole {
        OWNER,
        POSTER,
        UNLOCKER,
        ANSWERER,
        STRANGER,
    }

    enum WishActionType {
        ISSUED,
        APPLY,
        FINISHED,
        UNLOCK,
        CUT,
        TRANSFER,
        OWNER,
        REFERRAL_LINK,
        REFERRAL_APPLY,
        REFUND,
    }

    enum NFTMintConfigStatus {
        UPCOMING,
        LIVE,
        ENDED,
        COMING_SOON
    }

    @Getter
    enum NFTTierType {
        TIER0(0, "Genesis NFT", "Tier 0"),
        TIER1(1, "Zeek OG NFT", "Tier 1"),
        TIER2(2, "Zeek OG NFT", "Tier 2"),
        TIER3(3, "Zeek OG NFT", "Tier 3"),
        TIER4(4, "Zeek OG NFT", "Tier 4"),
        TIER5(5, "Zeek OG NFT", "Tier 5"),
        ;

        private final int code;
        private final String name;
        private final String shortName;

        NFTTierType(int code, String name, String shortName) {
            this.code = code;
            this.name = name;
            this.shortName = shortName;
        }

        public static NFTTierType getByCode(int code) {
            for (NFTTierType type : NFTTierType.values()) {
                if (type.getCode() == code) {
                    return type;
                }
            }
            return null;
        }
    }

    @Getter
    enum NftStatus {
        PENDING(0, "pending"),
        CLAIMABLE(0, "claimable"),
        CLAIMED(2, "claimed"),
        ;
        private final int value; // 用于排序
        private final String name;

        NftStatus(int value, String name) {
            this.value = value;
            this.name = name;
        }

        public static NftStatus getByName(String name) {
            for (NftStatus status : NftStatus.values()) {
                if (StringUtils.equals(status.getName(), name)) {
                    return status;
                }
            }
            return null;
        }
    }


    /**
     * 人工奖励记录状态
     */
    enum RewardStatus {
        PROCESSING, // 处理中
        DONE;   // 已完成
    }

    /**
     * monitor 监控的报错码枚举
     */
    enum MonitorErrorCode {
        KOL_TWEET_POST_ERROR,
        INVITE_CODE_GENERATE_FAIL,
        USER_BASIC_UPSERT_FAIL,
        CUSTOMER_MSG_PROCESS_FAIL,
        CUSTOMER_MSG_PROCESS_FAIL_BIND_WALLET,
        CUSTOMER_MSG_PROCESS_FAIL_VERIFY_INVITE_CODE,
        CUSTOMER_MSG_RECONSUME_TIMES_MORE_THAN_3,
        CHAIN_ERROR_GET_TOKEN_HOLDER,
        CHAIN_ERROR_GET_BALANCE,
        REFRESH_CODE_LIMIT_FAIL,
        QUESTS_WEB_REQUEST_FAIL,
        FINISH_WISH_FAIL,
        ACTIVE_WISH_FAIL,
        APPLY_WISH_FAIL,
        OSP_REQUEST_FAIL,
        PRICE_REQUEST_FAIL,
        GET_PROFILE_FROM_OSP_FAIL,
        ;
    }


    @Getter
    enum WalletTransactionType {
        SEND(0, ""),
        RECEIVE(1, ""),
        WISH_POST(2, ""),
        WISH_TRADE(3, ""),
        CLAIM_REWARDS(4, ""),
        UNLOCK_ANSWERS_WISH_BIDDED_TALENT(5, ""),
        UNLOCK_ANSWERS_WISH_UNLOCKED_ISSUER(6, ""),
        UNLOCK_ANSWERS_WISH_UNLOCKED_OWNER(7, ""),
        UNLOCK_ANSWERS_WISH_UNLOCKED_TALENT(8, ""),
        GET_AN_OFFER(9, ""),
        MINT_NFT(10, ""),
        WISH_UNLOCKED(11, ""),
        QUEST_POST(12, ""),
        QUEST_VERIFY(13, ""),
        WISH_REFUND(14, ""),
        QUEST_REFUND(15, ""),
        SWAP(16, ""),
        PREDICTION_POST(17, "Staking for Prediction"),
        PREDICTION_OUTCOME(18, "Pay for Answer"),
        PREDICTION_TRADE(19, "Trade Answer"),
        PREDICTION_CLAIM_MARGIN(20, "Return staked Prediction"),
        PREDICTION_CLAIM_FUNDING(21, "Prediction shares profit"),
        PREDICTION_CLAIM_FEE(22, "Prediction trading fee"),
        ;
        private final int code;
        private final String message;

        WalletTransactionType(int code, String message) {
            this.code = code;
            this.message = message;
        }

        public static WalletTransactionType getByCode(int code) {
            for (WalletTransactionType type : WalletTransactionType.values()) {
                if (type.getCode() == code) {
                    return type;
                }
            }
            return null;
        }

        public static WalletTransactionType getUnlockAnswersType(BigInteger scene, BigInteger role) {
            WishScene wishScene = WishScene.get(scene.intValue());
            WishParticipant wishParticipant = WishParticipant.get(role.intValue());
            switch (wishScene) {
                case Unlock -> {
                    switch (wishParticipant) {
                        case Issuer -> {
                            return UNLOCK_ANSWERS_WISH_UNLOCKED_ISSUER;
                        }
                        case Owner -> {
                            return UNLOCK_ANSWERS_WISH_UNLOCKED_OWNER;
                        }
                        case Talent -> {
                            return UNLOCK_ANSWERS_WISH_UNLOCKED_TALENT;
                        }
                    }
                }
                case Bid -> {
                    switch (wishParticipant) {
                        case Talent -> {
                            return UNLOCK_ANSWERS_WISH_BIDDED_TALENT;
                        }
                    }
                }
            }
            return null;
        }

    }

    @Getter
    enum WalletTransactionCategory {
        SEND(0),
        RECEIVE(1),
        WISH_POST(2),
        WISH_TRADE(3),
        CLAIM_REWARDS(4),
        UNLOCK_ANSWERS(5),
        GET_AN_OFFER(6),
        MINT_NFT(7),
        WISH_UNLOCKED(8),
        QUEST_POST(9),
        QUEST_VERIFY(10),
        WISH_REFUND(11),
        QUEST_REFUND(12),
        SWAP(13),
        PREDICTION_POST(14),
        PREDICTION_OUTCOME(15),
        PREDICTION_TRADE(16),
        PREDICTION_CLOSE(17),
        PREDICTION_CLAIM(18),

        ;
        private final int code;

        WalletTransactionCategory(int code) {
            this.code = code;
        }

        public static WalletTransactionCategory getByCode(int code) {
            for (WalletTransactionCategory category : WalletTransactionCategory.values()) {
                if (category.getCode() == code) {
                    return category;
                }
            }
            return null;
        }

    }

    @Getter
    enum WalletTransactionDirection {
        SEND(-1),
        RECEIVE(1),
        ;
        private final int code;

        WalletTransactionDirection(int code) {
            this.code = code;
        }

        public static WalletTransactionDirection getByCode(int code) {
            for (WalletTransactionDirection direction : WalletTransactionDirection.values()) {
                if (direction.getCode() == code) {
                    return direction;
                }
            }
            return null;
        }
    }

    @Getter
    enum ManualFeedStatus {
        VALID(0L),
        INVALID(1L),
        ;
        private final Long code;
        ManualFeedStatus(Long code) {
            this.code = code;
        }
    }

    enum OutcomeStatus {
        PENDING(0),
        NORMAL(1),
        PERFECT(2);

        private final int code;

        OutcomeStatus(int code) {
            this.code = code;
        }

        public int getCode() {
            return code;
        }

        public static ZeekConstants.OutcomeStatus get(int code) {
            return Arrays.stream(values()).filter(f -> f.code == code).findFirst().orElse(null);
        }
    }

    enum PredictionAction {
        POST(0),
        OUTCOME(1),
        VOTE(2),
        TRADE(3),
        CLAIM(4),
        CLOSE(5),
        ;
        private final int code;

        PredictionAction(int code) {
            this.code = code;
        }

        public int getCode() {
            return code;
        }

        public static ZeekConstants.PredictionAction get(int code) {
            return Arrays.stream(values()).filter(f -> f.code == code).findFirst().orElse(null);
        }
    }

    enum PredictionClaimStatus {
        NONE(0),
        CLAIMABLE(1),
        CLAIMED(2),
        ;
        private final int code;

        PredictionClaimStatus(int code) {
            this.code = code;
        }

        public int getCode() {
            return code;
        }

        public static ZeekConstants.PredictionClaimStatus get(int code) {
            return Arrays.stream(values()).filter(f -> f.code == code).findFirst().orElse(null);
        }
    }

    enum PredictionOracleType {
        Deek, // big deek energy 投票确定
        Manual, // 手动选出
        ;
    }

}
