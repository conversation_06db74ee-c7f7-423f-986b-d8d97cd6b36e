package com.zeek.core.common.util;

import org.web3j.utils.Numeric;
import org.web3j.crypto.Hash;
import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.util.Arrays;

public class Create2CloneCalculator {

    public static void main(String[] args) {
        System.out.println("=== Create2Clone 地址预测计算器 ===\n");

        try {
            // 测试参数
            String factoryAddress = "******************************************";
            String implementationMaster = "******************************************";
            BigInteger saltNonce = BigInteger.ZERO;
            String msgSender = "******************************************";

            // 构造函数数据
            byte[] constructorData = encodeConstructorData(
                    "******************************************", // pmSystem
                    "0x9c22ff5f21f0b81b113e63f7db6da94fedef11b2119b4088b89664fb9a3cb658", // questionId
                    BigInteger.valueOf(2), // outcomeSlotCount
                    BigInteger.valueOf(1749208985), // expiredAt
                    BigInteger.valueOf(20000000000000000L) // margin
            );

            System.out.println("输入参数:");
            System.out.println("Factory地址: " + factoryAddress);
            System.out.println("Implementation地址: " + implementationMaster);
            System.out.println("Salt Nonce: " + saltNonce);
            System.out.println("调用者地址: " + msgSender);
            System.out.println("构造函数数据长度: " + constructorData.length + " bytes");
            
            // 详细输出构造函数编码
            System.out.println("\n=== 构造函数数据验证 ===");
            System.out.println("完整构造函数数据: 0x" + bytesToHex(constructorData));
            System.out.println("pmSystem (32字节): 0x" + bytesToHex(Arrays.copyOfRange(constructorData, 0, 32)));
            System.out.println("questionId (32字节): 0x" + bytesToHex(Arrays.copyOfRange(constructorData, 32, 64)));
            System.out.println("outcomeSlotCount (32字节): 0x" + bytesToHex(Arrays.copyOfRange(constructorData, 64, 96)));
            System.out.println("expiredAt (32字节): 0x" + bytesToHex(Arrays.copyOfRange(constructorData, 96, 128)));
            System.out.println("margin (32字节): 0x" + bytesToHex(Arrays.copyOfRange(constructorData, 128, 160)));

            // 预测地址
            String predictedAddress = predictCreate2Address(
                    factoryAddress,
                    implementationMaster,
                    saltNonce,
                    msgSender,
                    constructorData
            );

            System.out.println("\n=== 结果 ===");
            System.out.println("预测的合约地址: " + predictedAddress);
            System.out.println("校验和地址: " + toChecksumAddress(predictedAddress));

            // 测试不同参数
            testDifferentParameters();

        } catch (Exception e) {
            System.err.println("计算失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 预测 create2Clone 部署的合约地址
     */
    public static String predictCreate2Address(
            String deployerAddress,
            String targetAddress,
            BigInteger saltNonce,
            String msgSender,
            byte[] constructorData) {

        System.out.println("\n=== 详细计算步骤 ===");
        
        // 1. 生成代理合约字节码
        byte[] proxyBytecode = generateProxyBytecode(deployerAddress, targetAddress, constructorData);
        System.out.println("1. 完整initCode长度: " + proxyBytecode.length + " bytes");
        System.out.println("   initCode前64字节: " + bytesToHex(Arrays.copyOfRange(proxyBytecode, 0, Math.min(64, proxyBytecode.length))));

        // 2. 计算 salt
        byte[] salt = calculateSalt(msgSender, saltNonce);
        System.out.println("2. Salt: 0x" + bytesToHex(salt));

        // 3. 计算initCode的hash
        byte[] initCodeHash = keccak256(proxyBytecode);
        System.out.println("3. initCode hash: 0x" + bytesToHex(initCodeHash));

        // 4. 使用 CREATE2 公式计算地址
        String result = calculateCreate2Address(deployerAddress, salt, proxyBytecode);
        System.out.println("4. 最终计算的地址: " + result);
        
        return result;
    }

    /**
     * 生成代理合约字节码 - 严格模拟Solidity assembly的mstore重叠行为
     * 重新分析每个mstore操作的覆盖效果
     */
    private static byte[] generateProxyBytecode(String deployerAddress, String targetAddress, byte[] constructorData) {
        // 创建内存缓冲区来严格模拟Solidity的内存操作
        // clone = new bytes(99 + consData.length)，但assembly只操作前99字节的代理部分
        byte[] memory = new byte[148]; // 确保足够大来容纳所有mstore操作
        
        System.out.println("   模拟Solidity assembly mstore操作:");
        
        // 按照Solidity assembly的精确顺序执行mstore操作
        // 每个mstore(offset, value)都会在指定偏移写入32字节
        
        // 1. mstore(add(clone, 0x20), 0x3d3d606380380380913d393d73bebebebebebebebebebebebebebebebebebebe)
        //    在相对偏移0x20写入32字节
        byte[] value1 = hexToBytes("3d3d606380380380913d393d73bebebebebebebebebebebebebebebebebebebe");
        System.arraycopy(value1, 0, memory, 0x20, 32);
        System.out.println("   step1 (0x20): " + bytesToHex(Arrays.copyOfRange(memory, 0x20, 0x20 + 32)));
        
        // 2. mstore(add(clone, 0x2d), mul(address(), 0x01000000000000000000000000))
        //    在相对偏移0x2d写入32字节
        //    mul操作: address() * 2^96，这会将20字节地址左移到32字节值的高位
        byte[] factoryBytes = hexToBytes(cleanHexPrefix(deployerAddress));
        byte[] value2 = new byte[32];
        // 地址放在32字节的开头（高位），剩余12字节为0
        System.arraycopy(factoryBytes, 0, value2, 0, 20);
        System.arraycopy(value2, 0, memory, 0x2d, 32);
        System.out.println("   step2 (0x2d): " + bytesToHex(Arrays.copyOfRange(memory, 0x2d, 0x2d + 32)));
        
        // 3. mstore(add(clone, 0x41), 0x5af4602a57600080fd5b602d8060366000396000f3363d3d373d3d3d363d73be)
        //    在相对偏移0x41写入32字节
        byte[] value3 = hexToBytes("5af4602a57600080fd5b602d8060366000396000f3363d3d373d3d3d363d73be");
        System.arraycopy(value3, 0, memory, 0x41, 32);
        System.out.println("   step3 (0x41): " + bytesToHex(Arrays.copyOfRange(memory, 0x41, 0x41 + 32)));
        
        // 4. mstore(add(clone, 0x60), mul(target, 0x01000000000000000000000000))
        //    在相对偏移0x60写入32字节
        byte[] targetBytes = hexToBytes(cleanHexPrefix(targetAddress));
        byte[] value4 = new byte[32];
        // target地址也放在32字节的开头（高位）
        System.arraycopy(targetBytes, 0, value4, 0, 20);
        System.arraycopy(value4, 0, memory, 0x60, 32);
        System.out.println("   step4 (0x60): " + bytesToHex(Arrays.copyOfRange(memory, 0x60, 0x60 + 32)));
        
        // 5. mstore(add(clone, 116), 0x5af43d82803e903d91602b57fd5bf30000000000000000000000000000000000)
        //    在相对偏移116 (0x74)写入32字节
        byte[] value5 = hexToBytes("5af43d82803e903d91602b57fd5bf30000000000000000000000000000000000");
        System.arraycopy(value5, 0, memory, 116, 32);
        System.out.println("   step5 (116): " + bytesToHex(Arrays.copyOfRange(memory, 116, 116 + 32)));
        
        // 提取最终的99字节代理字节码（从偏移0x20开始，长度99）
        byte[] finalBytecode = new byte[99];
        System.arraycopy(memory, 0x20, finalBytecode, 0, 99);
        
        System.out.println("   最终内存状态 (0x20-0x83): " + bytesToHex(finalBytecode));
        
        // 连接构造函数数据
        byte[] result = new byte[99 + constructorData.length];
        System.arraycopy(finalBytecode, 0, result, 0, 99);
        System.arraycopy(constructorData, 0, result, 99, constructorData.length);
        
        return result;
    }

    /**
     * 计算 salt (与Solidity中的keccak256(abi.encode(msg.sender, saltNonce))相同)
     */
    private static byte[] calculateSalt(String msgSender, BigInteger saltNonce) {
        ByteBuffer buffer = ByteBuffer.allocate(64);

        // msg.sender (左填充到32字节)
        byte[] senderBytes = hexToBytes(cleanHexPrefix(msgSender));
        buffer.put(new byte[12]); // 填充12个0字节
        buffer.put(senderBytes);  // 20字节地址

        // saltNonce (32字节大端序)
        byte[] nonceBytes = saltNonce.toByteArray();
        buffer.put(new byte[32 - nonceBytes.length]); // 左填充0
        buffer.put(nonceBytes);

        byte[] encodedData = buffer.array();
        System.out.println("   Salt编码数据 (64字节): 0x" + bytesToHex(encodedData));
        System.out.println("   其中msgSender: " + msgSender);
        System.out.println("   其中saltNonce: " + saltNonce);
        
        byte[] result = keccak256(encodedData);
        return result;
    }

    /**
     * 使用 CREATE2 公式计算地址
     * address = keccak256(0xff + deployerAddress + salt + keccak256(initCode))[12:]
     */
    private static String calculateCreate2Address(String deployerAddress, byte[] salt, byte[] initCode) {
        System.out.println("   CREATE2计算详细信息:");
        System.out.println("   deployerAddress: " + deployerAddress);
        System.out.println("   salt: 0x" + bytesToHex(salt));
        System.out.println("   initCode长度: " + initCode.length);
        
        ByteBuffer buffer = ByteBuffer.allocate(1 + 20 + 32 + 32);

        // 0xff
        buffer.put((byte) 0xff);

        // deployer address (20字节)
        byte[] deployerBytes = hexToBytes(cleanHexPrefix(deployerAddress));
        buffer.put(deployerBytes);

        // salt (32字节)
        buffer.put(salt);

        // keccak256(initCode) (32字节)
        byte[] initCodeHash = keccak256(initCode);
        buffer.put(initCodeHash);
        
        byte[] create2Input = buffer.array();
        System.out.println("   CREATE2输入 (85字节): 0x" + bytesToHex(create2Input));

        // 计算最终哈希并取后20字节作为地址
        byte[] hash = keccak256(create2Input);
        System.out.println("   CREATE2哈希: 0x" + bytesToHex(hash));
        
        byte[] addressBytes = Arrays.copyOfRange(hash, 12, 32);
        String result = "0x" + bytesToHex(addressBytes);
        
        System.out.println("   最终地址: " + result);

        return result;
    }

    /**
     * ABI编码构造函数数据
     */
    public static byte[] encodeConstructorData(String pmSystem, String questionId,
                                                BigInteger outcomeSlotCount, BigInteger expiredAt,
                                                BigInteger margin) {
        ByteBuffer buffer = ByteBuffer.allocate(160); // 5个参数 * 32字节

        // pmSystem (address, 32字节)
        byte[] pmSystemBytes = hexToBytes(cleanHexPrefix(pmSystem));
        buffer.put(new byte[12]);
        buffer.put(pmSystemBytes);

        // questionId (bytes32, 32字节)
        buffer.put(hexToBytes(cleanHexPrefix(questionId)));

        // outcomeSlotCount (uint256, 32字节)
        putUint256(buffer, outcomeSlotCount);

        // expiredAt (uint256, 32字节)
        putUint256(buffer, expiredAt);

        // margin (uint256, 32字节)
        putUint256(buffer, margin);

        return buffer.array();
    }

    /**
     * 将BigInteger写入ByteBuffer作为32字节uint256
     */
    private static void putUint256(ByteBuffer buffer, BigInteger value) {
        byte[] valueBytes = value.toByteArray();
        if (valueBytes.length > 32) {
            valueBytes = Arrays.copyOfRange(valueBytes, valueBytes.length - 32, valueBytes.length);
        }
        buffer.put(new byte[32 - valueBytes.length]);
        buffer.put(valueBytes);
    }

    /**
     * 测试不同参数的影响
     */
    private static void testDifferentParameters() throws Exception {
        System.out.println("\n=== 参数变化测试 ===");

        String baseFactory = "0x1234567890123456789012345678901234567890";
        String baseImplementation = "0xabcdefabcdefabcdefabcdefabcdefabcdefabcd";
        String baseSender = "0x9876543210987654321098765432109876543210";
        byte[] baseConstructorData = encodeConstructorData(
                "0x1111111111111111111111111111111111111111",
                "0x2222222222222222222222222222222222222222222222222222222222222222",
                BigInteger.valueOf(3),
                BigInteger.valueOf(1700000000),
                BigInteger.valueOf(1000)
        );

        // 相同参数测试
        String addr1 = predictCreate2Address(baseFactory, baseImplementation, BigInteger.ZERO, baseSender, baseConstructorData);
        String addr2 = predictCreate2Address(baseFactory, baseImplementation, BigInteger.ZERO, baseSender, baseConstructorData);
        System.out.println("相同参数测试: " + addr1.equals(addr2));

        // 不同Salt测试
        String addr3 = predictCreate2Address(baseFactory, baseImplementation, BigInteger.ONE, baseSender, baseConstructorData);
        System.out.println("不同Salt测试: " + !addr1.equals(addr3));

        // 不同调用者测试
        String addr4 = predictCreate2Address(baseFactory, baseImplementation, BigInteger.ZERO, "0x1111111111111111111111111111111111111111", baseConstructorData);
        System.out.println("不同调用者测试: " + !addr1.equals(addr4));

        System.out.println("所有测试通过！");
    }

    /**
     * 计算Keccak-256哈希 (使用web3j)
     */
    private static byte[] keccak256(byte[] input) {
        return Hash.sha3(input);
    }

    /**
     * 转换为校验和地址 (EIP-55)
     */
    private static String toChecksumAddress(String address) {
        try {
            String cleanAddress = cleanHexPrefix(address).toLowerCase();
            String hash = bytesToHex(keccak256(cleanAddress.getBytes()));

            StringBuilder checksumAddress = new StringBuilder("0x");
            for (int i = 0; i < cleanAddress.length(); i++) {
                char c = cleanAddress.charAt(i);
                if (Character.isDigit(c)) {
                    checksumAddress.append(c);
                } else {
                    char hashChar = hash.charAt(i);
                    if (hashChar >= '8') {
                        checksumAddress.append(Character.toUpperCase(c));
                    } else {
                        checksumAddress.append(c);
                    }
                }
            }
            return checksumAddress.toString();
        } catch (Exception e) {
            return address;
        }
    }

    /**
     * hex字符串转字节数组
     */
    private static byte[] hexToBytes(String hex) {
        int len = hex.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                    + Character.digit(hex.charAt(i + 1), 16));
        }
        return data;
    }

    /**
     * 字节数组转hex字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    /**
     * 清理hex前缀
     */
    private static String cleanHexPrefix(String hex) {
        return hex.startsWith("0x") ? hex.substring(2) : hex;
    }
}