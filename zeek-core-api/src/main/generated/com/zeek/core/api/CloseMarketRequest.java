// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: RemotePredictionService.proto

package com.zeek.core.api;

/**
 * Protobuf type {@code com.zeek.core.api.CloseMarketRequest}
 */
public final class CloseMarketRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.zeek.core.api.CloseMarketRequest)
    CloseMarketRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use CloseMarketRequest.newBuilder() to construct.
  private CloseMarketRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private CloseMarketRequest() {
    marketId_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new CloseMarketRequest();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.zeek.core.api.RemotePredictionServiceOuterClass.internal_static_com_zeek_core_api_CloseMarketRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.zeek.core.api.RemotePredictionServiceOuterClass.internal_static_com_zeek_core_api_CloseMarketRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.zeek.core.api.CloseMarketRequest.class, com.zeek.core.api.CloseMarketRequest.Builder.class);
  }

  public static final int MARKETID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object marketId_ = "";
  /**
   * <code>string marketId = 1;</code>
   * @return The marketId.
   */
  @java.lang.Override
  public java.lang.String getMarketId() {
    java.lang.Object ref = marketId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      marketId_ = s;
      return s;
    }
  }
  /**
   * <code>string marketId = 1;</code>
   * @return The bytes for marketId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMarketIdBytes() {
    java.lang.Object ref = marketId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      marketId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SLOT_FIELD_NUMBER = 2;
  private int slot_ = 0;
  /**
   * <code>int32 slot = 2;</code>
   * @return The slot.
   */
  @java.lang.Override
  public int getSlot() {
    return slot_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(marketId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, marketId_);
    }
    if (slot_ != 0) {
      output.writeInt32(2, slot_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(marketId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, marketId_);
    }
    if (slot_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(2, slot_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.zeek.core.api.CloseMarketRequest)) {
      return super.equals(obj);
    }
    com.zeek.core.api.CloseMarketRequest other = (com.zeek.core.api.CloseMarketRequest) obj;

    if (!getMarketId()
        .equals(other.getMarketId())) return false;
    if (getSlot()
        != other.getSlot()) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + MARKETID_FIELD_NUMBER;
    hash = (53 * hash) + getMarketId().hashCode();
    hash = (37 * hash) + SLOT_FIELD_NUMBER;
    hash = (53 * hash) + getSlot();
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.zeek.core.api.CloseMarketRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zeek.core.api.CloseMarketRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zeek.core.api.CloseMarketRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zeek.core.api.CloseMarketRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zeek.core.api.CloseMarketRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zeek.core.api.CloseMarketRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zeek.core.api.CloseMarketRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zeek.core.api.CloseMarketRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.zeek.core.api.CloseMarketRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.zeek.core.api.CloseMarketRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.zeek.core.api.CloseMarketRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zeek.core.api.CloseMarketRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.zeek.core.api.CloseMarketRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.zeek.core.api.CloseMarketRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.zeek.core.api.CloseMarketRequest)
      com.zeek.core.api.CloseMarketRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.zeek.core.api.RemotePredictionServiceOuterClass.internal_static_com_zeek_core_api_CloseMarketRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.zeek.core.api.RemotePredictionServiceOuterClass.internal_static_com_zeek_core_api_CloseMarketRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.zeek.core.api.CloseMarketRequest.class, com.zeek.core.api.CloseMarketRequest.Builder.class);
    }

    // Construct using com.zeek.core.api.CloseMarketRequest.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      marketId_ = "";
      slot_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.zeek.core.api.RemotePredictionServiceOuterClass.internal_static_com_zeek_core_api_CloseMarketRequest_descriptor;
    }

    @java.lang.Override
    public com.zeek.core.api.CloseMarketRequest getDefaultInstanceForType() {
      return com.zeek.core.api.CloseMarketRequest.getDefaultInstance();
    }

    @java.lang.Override
    public com.zeek.core.api.CloseMarketRequest build() {
      com.zeek.core.api.CloseMarketRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.zeek.core.api.CloseMarketRequest buildPartial() {
      com.zeek.core.api.CloseMarketRequest result = new com.zeek.core.api.CloseMarketRequest(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.zeek.core.api.CloseMarketRequest result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.marketId_ = marketId_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.slot_ = slot_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.zeek.core.api.CloseMarketRequest) {
        return mergeFrom((com.zeek.core.api.CloseMarketRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.zeek.core.api.CloseMarketRequest other) {
      if (other == com.zeek.core.api.CloseMarketRequest.getDefaultInstance()) return this;
      if (!other.getMarketId().isEmpty()) {
        marketId_ = other.marketId_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (other.getSlot() != 0) {
        setSlot(other.getSlot());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              marketId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 16: {
              slot_ = input.readInt32();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object marketId_ = "";
    /**
     * <code>string marketId = 1;</code>
     * @return The marketId.
     */
    public java.lang.String getMarketId() {
      java.lang.Object ref = marketId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        marketId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string marketId = 1;</code>
     * @return The bytes for marketId.
     */
    public com.google.protobuf.ByteString
        getMarketIdBytes() {
      java.lang.Object ref = marketId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        marketId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string marketId = 1;</code>
     * @param value The marketId to set.
     * @return This builder for chaining.
     */
    public Builder setMarketId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      marketId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>string marketId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearMarketId() {
      marketId_ = getDefaultInstance().getMarketId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>string marketId = 1;</code>
     * @param value The bytes for marketId to set.
     * @return This builder for chaining.
     */
    public Builder setMarketIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      marketId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private int slot_ ;
    /**
     * <code>int32 slot = 2;</code>
     * @return The slot.
     */
    @java.lang.Override
    public int getSlot() {
      return slot_;
    }
    /**
     * <code>int32 slot = 2;</code>
     * @param value The slot to set.
     * @return This builder for chaining.
     */
    public Builder setSlot(int value) {

      slot_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>int32 slot = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearSlot() {
      bitField0_ = (bitField0_ & ~0x00000002);
      slot_ = 0;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.zeek.core.api.CloseMarketRequest)
  }

  // @@protoc_insertion_point(class_scope:com.zeek.core.api.CloseMarketRequest)
  private static final com.zeek.core.api.CloseMarketRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.zeek.core.api.CloseMarketRequest();
  }

  public static com.zeek.core.api.CloseMarketRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<CloseMarketRequest>
      PARSER = new com.google.protobuf.AbstractParser<CloseMarketRequest>() {
    @java.lang.Override
    public CloseMarketRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<CloseMarketRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<CloseMarketRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.zeek.core.api.CloseMarketRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

