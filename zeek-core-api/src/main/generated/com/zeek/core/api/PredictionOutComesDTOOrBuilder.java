// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: RemotePredictionService.proto

package com.zeek.core.api;

public interface PredictionOutComesDTOOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zeek.core.api.PredictionOutComesDTO)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string customerId = 1;</code>
   * @return The customerId.
   */
  java.lang.String getCustomerId();
  /**
   * <code>string customerId = 1;</code>
   * @return The bytes for customerId.
   */
  com.google.protobuf.ByteString
      getCustomerIdBytes();

  /**
   * <code>string handle = 2;</code>
   * @return The handle.
   */
  java.lang.String getHandle();
  /**
   * <code>string handle = 2;</code>
   * @return The bytes for handle.
   */
  com.google.protobuf.ByteString
      getHandleBytes();

  /**
   * <code>string content = 3;</code>
   * @return The content.
   */
  java.lang.String getContent();
  /**
   * <code>string content = 3;</code>
   * @return The bytes for content.
   */
  com.google.protobuf.ByteString
      getContentBytes();

  /**
   * <code>repeated string medias = 4;</code>
   * @return A list containing the medias.
   */
  java.util.List<java.lang.String>
      getMediasList();
  /**
   * <code>repeated string medias = 4;</code>
   * @return The count of medias.
   */
  int getMediasCount();
  /**
   * <code>repeated string medias = 4;</code>
   * @param index The index of the element to return.
   * @return The medias at the given index.
   */
  java.lang.String getMedias(int index);
  /**
   * <code>repeated string medias = 4;</code>
   * @param index The index of the value to return.
   * @return The bytes of the medias at the given index.
   */
  com.google.protobuf.ByteString
      getMediasBytes(int index);

  /**
   * <code>int64 slot = 5;</code>
   * @return The slot.
   */
  long getSlot();

  /**
   * <code>string status = 6;</code>
   * @return The status.
   */
  java.lang.String getStatus();
  /**
   * <code>string status = 6;</code>
   * @return The bytes for status.
   */
  com.google.protobuf.ByteString
      getStatusBytes();

  /**
   * <code>string volume = 7;</code>
   * @return The volume.
   */
  java.lang.String getVolume();
  /**
   * <code>string volume = 7;</code>
   * @return The bytes for volume.
   */
  com.google.protobuf.ByteString
      getVolumeBytes();

  /**
   * <code>string myVote = 8;</code>
   * @return The myVote.
   */
  java.lang.String getMyVote();
  /**
   * <code>string myVote = 8;</code>
   * @return The bytes for myVote.
   */
  com.google.protobuf.ByteString
      getMyVoteBytes();

  /**
   * <code>string vote = 9;</code>
   * @return The vote.
   */
  java.lang.String getVote();
  /**
   * <code>string vote = 9;</code>
   * @return The bytes for vote.
   */
  com.google.protobuf.ByteString
      getVoteBytes();

  /**
   * <code>string nickName = 10;</code>
   * @return The nickName.
   */
  java.lang.String getNickName();
  /**
   * <code>string nickName = 10;</code>
   * @return The bytes for nickName.
   */
  com.google.protobuf.ByteString
      getNickNameBytes();

  /**
   * <code>int64 created = 11;</code>
   * @return The created.
   */
  long getCreated();

  /**
   * <code>float chance = 12;</code>
   * @return The chance.
   */
  float getChance();
}
