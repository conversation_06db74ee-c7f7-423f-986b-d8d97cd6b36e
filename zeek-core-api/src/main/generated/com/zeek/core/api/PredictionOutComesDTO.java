// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: RemotePredictionService.proto

package com.zeek.core.api;

/**
 * Protobuf type {@code com.zeek.core.api.PredictionOutComesDTO}
 */
public final class PredictionOutComesDTO extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.zeek.core.api.PredictionOutComesDTO)
    PredictionOutComesDTOOrBuilder {
private static final long serialVersionUID = 0L;
  // Use PredictionOutComesDTO.newBuilder() to construct.
  private PredictionOutComesDTO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private PredictionOutComesDTO() {
    customerId_ = "";
    handle_ = "";
    content_ = "";
    medias_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    status_ = "";
    volume_ = "";
    myVote_ = "";
    vote_ = "";
    nickName_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new PredictionOutComesDTO();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.zeek.core.api.RemotePredictionServiceOuterClass.internal_static_com_zeek_core_api_PredictionOutComesDTO_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.zeek.core.api.RemotePredictionServiceOuterClass.internal_static_com_zeek_core_api_PredictionOutComesDTO_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.zeek.core.api.PredictionOutComesDTO.class, com.zeek.core.api.PredictionOutComesDTO.Builder.class);
  }

  public static final int CUSTOMERID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object customerId_ = "";
  /**
   * <code>string customerId = 1;</code>
   * @return The customerId.
   */
  @java.lang.Override
  public java.lang.String getCustomerId() {
    java.lang.Object ref = customerId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      customerId_ = s;
      return s;
    }
  }
  /**
   * <code>string customerId = 1;</code>
   * @return The bytes for customerId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCustomerIdBytes() {
    java.lang.Object ref = customerId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      customerId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int HANDLE_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object handle_ = "";
  /**
   * <code>string handle = 2;</code>
   * @return The handle.
   */
  @java.lang.Override
  public java.lang.String getHandle() {
    java.lang.Object ref = handle_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      handle_ = s;
      return s;
    }
  }
  /**
   * <code>string handle = 2;</code>
   * @return The bytes for handle.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getHandleBytes() {
    java.lang.Object ref = handle_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      handle_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CONTENT_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object content_ = "";
  /**
   * <code>string content = 3;</code>
   * @return The content.
   */
  @java.lang.Override
  public java.lang.String getContent() {
    java.lang.Object ref = content_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      content_ = s;
      return s;
    }
  }
  /**
   * <code>string content = 3;</code>
   * @return The bytes for content.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getContentBytes() {
    java.lang.Object ref = content_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      content_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MEDIAS_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private com.google.protobuf.LazyStringArrayList medias_ =
      com.google.protobuf.LazyStringArrayList.emptyList();
  /**
   * <code>repeated string medias = 4;</code>
   * @return A list containing the medias.
   */
  public com.google.protobuf.ProtocolStringList
      getMediasList() {
    return medias_;
  }
  /**
   * <code>repeated string medias = 4;</code>
   * @return The count of medias.
   */
  public int getMediasCount() {
    return medias_.size();
  }
  /**
   * <code>repeated string medias = 4;</code>
   * @param index The index of the element to return.
   * @return The medias at the given index.
   */
  public java.lang.String getMedias(int index) {
    return medias_.get(index);
  }
  /**
   * <code>repeated string medias = 4;</code>
   * @param index The index of the value to return.
   * @return The bytes of the medias at the given index.
   */
  public com.google.protobuf.ByteString
      getMediasBytes(int index) {
    return medias_.getByteString(index);
  }

  public static final int SLOT_FIELD_NUMBER = 5;
  private long slot_ = 0L;
  /**
   * <code>int64 slot = 5;</code>
   * @return The slot.
   */
  @java.lang.Override
  public long getSlot() {
    return slot_;
  }

  public static final int STATUS_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile java.lang.Object status_ = "";
  /**
   * <code>string status = 6;</code>
   * @return The status.
   */
  @java.lang.Override
  public java.lang.String getStatus() {
    java.lang.Object ref = status_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      status_ = s;
      return s;
    }
  }
  /**
   * <code>string status = 6;</code>
   * @return The bytes for status.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getStatusBytes() {
    java.lang.Object ref = status_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      status_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int VOLUME_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private volatile java.lang.Object volume_ = "";
  /**
   * <code>string volume = 7;</code>
   * @return The volume.
   */
  @java.lang.Override
  public java.lang.String getVolume() {
    java.lang.Object ref = volume_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      volume_ = s;
      return s;
    }
  }
  /**
   * <code>string volume = 7;</code>
   * @return The bytes for volume.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getVolumeBytes() {
    java.lang.Object ref = volume_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      volume_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MYVOTE_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private volatile java.lang.Object myVote_ = "";
  /**
   * <code>string myVote = 8;</code>
   * @return The myVote.
   */
  @java.lang.Override
  public java.lang.String getMyVote() {
    java.lang.Object ref = myVote_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      myVote_ = s;
      return s;
    }
  }
  /**
   * <code>string myVote = 8;</code>
   * @return The bytes for myVote.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMyVoteBytes() {
    java.lang.Object ref = myVote_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      myVote_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int VOTE_FIELD_NUMBER = 9;
  @SuppressWarnings("serial")
  private volatile java.lang.Object vote_ = "";
  /**
   * <code>string vote = 9;</code>
   * @return The vote.
   */
  @java.lang.Override
  public java.lang.String getVote() {
    java.lang.Object ref = vote_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      vote_ = s;
      return s;
    }
  }
  /**
   * <code>string vote = 9;</code>
   * @return The bytes for vote.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getVoteBytes() {
    java.lang.Object ref = vote_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      vote_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int NICKNAME_FIELD_NUMBER = 10;
  @SuppressWarnings("serial")
  private volatile java.lang.Object nickName_ = "";
  /**
   * <code>string nickName = 10;</code>
   * @return The nickName.
   */
  @java.lang.Override
  public java.lang.String getNickName() {
    java.lang.Object ref = nickName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      nickName_ = s;
      return s;
    }
  }
  /**
   * <code>string nickName = 10;</code>
   * @return The bytes for nickName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNickNameBytes() {
    java.lang.Object ref = nickName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      nickName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CREATED_FIELD_NUMBER = 11;
  private long created_ = 0L;
  /**
   * <code>int64 created = 11;</code>
   * @return The created.
   */
  @java.lang.Override
  public long getCreated() {
    return created_;
  }

  public static final int CHANCE_FIELD_NUMBER = 12;
  private float chance_ = 0F;
  /**
   * <code>float chance = 12;</code>
   * @return The chance.
   */
  @java.lang.Override
  public float getChance() {
    return chance_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(customerId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, customerId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(handle_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, handle_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(content_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, content_);
    }
    for (int i = 0; i < medias_.size(); i++) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, medias_.getRaw(i));
    }
    if (slot_ != 0L) {
      output.writeInt64(5, slot_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(status_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, status_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(volume_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, volume_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(myVote_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, myVote_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(vote_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, vote_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(nickName_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 10, nickName_);
    }
    if (created_ != 0L) {
      output.writeInt64(11, created_);
    }
    if (java.lang.Float.floatToRawIntBits(chance_) != 0) {
      output.writeFloat(12, chance_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(customerId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, customerId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(handle_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, handle_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(content_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, content_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < medias_.size(); i++) {
        dataSize += computeStringSizeNoTag(medias_.getRaw(i));
      }
      size += dataSize;
      size += 1 * getMediasList().size();
    }
    if (slot_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(5, slot_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(status_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, status_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(volume_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, volume_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(myVote_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, myVote_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(vote_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, vote_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(nickName_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, nickName_);
    }
    if (created_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(11, created_);
    }
    if (java.lang.Float.floatToRawIntBits(chance_) != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeFloatSize(12, chance_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.zeek.core.api.PredictionOutComesDTO)) {
      return super.equals(obj);
    }
    com.zeek.core.api.PredictionOutComesDTO other = (com.zeek.core.api.PredictionOutComesDTO) obj;

    if (!getCustomerId()
        .equals(other.getCustomerId())) return false;
    if (!getHandle()
        .equals(other.getHandle())) return false;
    if (!getContent()
        .equals(other.getContent())) return false;
    if (!getMediasList()
        .equals(other.getMediasList())) return false;
    if (getSlot()
        != other.getSlot()) return false;
    if (!getStatus()
        .equals(other.getStatus())) return false;
    if (!getVolume()
        .equals(other.getVolume())) return false;
    if (!getMyVote()
        .equals(other.getMyVote())) return false;
    if (!getVote()
        .equals(other.getVote())) return false;
    if (!getNickName()
        .equals(other.getNickName())) return false;
    if (getCreated()
        != other.getCreated()) return false;
    if (java.lang.Float.floatToIntBits(getChance())
        != java.lang.Float.floatToIntBits(
            other.getChance())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + CUSTOMERID_FIELD_NUMBER;
    hash = (53 * hash) + getCustomerId().hashCode();
    hash = (37 * hash) + HANDLE_FIELD_NUMBER;
    hash = (53 * hash) + getHandle().hashCode();
    hash = (37 * hash) + CONTENT_FIELD_NUMBER;
    hash = (53 * hash) + getContent().hashCode();
    if (getMediasCount() > 0) {
      hash = (37 * hash) + MEDIAS_FIELD_NUMBER;
      hash = (53 * hash) + getMediasList().hashCode();
    }
    hash = (37 * hash) + SLOT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getSlot());
    hash = (37 * hash) + STATUS_FIELD_NUMBER;
    hash = (53 * hash) + getStatus().hashCode();
    hash = (37 * hash) + VOLUME_FIELD_NUMBER;
    hash = (53 * hash) + getVolume().hashCode();
    hash = (37 * hash) + MYVOTE_FIELD_NUMBER;
    hash = (53 * hash) + getMyVote().hashCode();
    hash = (37 * hash) + VOTE_FIELD_NUMBER;
    hash = (53 * hash) + getVote().hashCode();
    hash = (37 * hash) + NICKNAME_FIELD_NUMBER;
    hash = (53 * hash) + getNickName().hashCode();
    hash = (37 * hash) + CREATED_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getCreated());
    hash = (37 * hash) + CHANCE_FIELD_NUMBER;
    hash = (53 * hash) + java.lang.Float.floatToIntBits(
        getChance());
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.zeek.core.api.PredictionOutComesDTO parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zeek.core.api.PredictionOutComesDTO parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zeek.core.api.PredictionOutComesDTO parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zeek.core.api.PredictionOutComesDTO parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zeek.core.api.PredictionOutComesDTO parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.zeek.core.api.PredictionOutComesDTO parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.zeek.core.api.PredictionOutComesDTO parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zeek.core.api.PredictionOutComesDTO parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.zeek.core.api.PredictionOutComesDTO parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.zeek.core.api.PredictionOutComesDTO parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.zeek.core.api.PredictionOutComesDTO parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.zeek.core.api.PredictionOutComesDTO parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.zeek.core.api.PredictionOutComesDTO prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.zeek.core.api.PredictionOutComesDTO}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.zeek.core.api.PredictionOutComesDTO)
      com.zeek.core.api.PredictionOutComesDTOOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.zeek.core.api.RemotePredictionServiceOuterClass.internal_static_com_zeek_core_api_PredictionOutComesDTO_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.zeek.core.api.RemotePredictionServiceOuterClass.internal_static_com_zeek_core_api_PredictionOutComesDTO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.zeek.core.api.PredictionOutComesDTO.class, com.zeek.core.api.PredictionOutComesDTO.Builder.class);
    }

    // Construct using com.zeek.core.api.PredictionOutComesDTO.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      customerId_ = "";
      handle_ = "";
      content_ = "";
      medias_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      slot_ = 0L;
      status_ = "";
      volume_ = "";
      myVote_ = "";
      vote_ = "";
      nickName_ = "";
      created_ = 0L;
      chance_ = 0F;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.zeek.core.api.RemotePredictionServiceOuterClass.internal_static_com_zeek_core_api_PredictionOutComesDTO_descriptor;
    }

    @java.lang.Override
    public com.zeek.core.api.PredictionOutComesDTO getDefaultInstanceForType() {
      return com.zeek.core.api.PredictionOutComesDTO.getDefaultInstance();
    }

    @java.lang.Override
    public com.zeek.core.api.PredictionOutComesDTO build() {
      com.zeek.core.api.PredictionOutComesDTO result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.zeek.core.api.PredictionOutComesDTO buildPartial() {
      com.zeek.core.api.PredictionOutComesDTO result = new com.zeek.core.api.PredictionOutComesDTO(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.zeek.core.api.PredictionOutComesDTO result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.customerId_ = customerId_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.handle_ = handle_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.content_ = content_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        medias_.makeImmutable();
        result.medias_ = medias_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.slot_ = slot_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.status_ = status_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.volume_ = volume_;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.myVote_ = myVote_;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.vote_ = vote_;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.nickName_ = nickName_;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.created_ = created_;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.chance_ = chance_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.zeek.core.api.PredictionOutComesDTO) {
        return mergeFrom((com.zeek.core.api.PredictionOutComesDTO)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.zeek.core.api.PredictionOutComesDTO other) {
      if (other == com.zeek.core.api.PredictionOutComesDTO.getDefaultInstance()) return this;
      if (!other.getCustomerId().isEmpty()) {
        customerId_ = other.customerId_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (!other.getHandle().isEmpty()) {
        handle_ = other.handle_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (!other.getContent().isEmpty()) {
        content_ = other.content_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (!other.medias_.isEmpty()) {
        if (medias_.isEmpty()) {
          medias_ = other.medias_;
          bitField0_ |= 0x00000008;
        } else {
          ensureMediasIsMutable();
          medias_.addAll(other.medias_);
        }
        onChanged();
      }
      if (other.getSlot() != 0L) {
        setSlot(other.getSlot());
      }
      if (!other.getStatus().isEmpty()) {
        status_ = other.status_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      if (!other.getVolume().isEmpty()) {
        volume_ = other.volume_;
        bitField0_ |= 0x00000040;
        onChanged();
      }
      if (!other.getMyVote().isEmpty()) {
        myVote_ = other.myVote_;
        bitField0_ |= 0x00000080;
        onChanged();
      }
      if (!other.getVote().isEmpty()) {
        vote_ = other.vote_;
        bitField0_ |= 0x00000100;
        onChanged();
      }
      if (!other.getNickName().isEmpty()) {
        nickName_ = other.nickName_;
        bitField0_ |= 0x00000200;
        onChanged();
      }
      if (other.getCreated() != 0L) {
        setCreated(other.getCreated());
      }
      if (other.getChance() != 0F) {
        setChance(other.getChance());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              customerId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              handle_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 26: {
              content_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              java.lang.String s = input.readStringRequireUtf8();
              ensureMediasIsMutable();
              medias_.add(s);
              break;
            } // case 34
            case 40: {
              slot_ = input.readInt64();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 50: {
              status_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            case 58: {
              volume_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            case 66: {
              myVote_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000080;
              break;
            } // case 66
            case 74: {
              vote_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000100;
              break;
            } // case 74
            case 82: {
              nickName_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000200;
              break;
            } // case 82
            case 88: {
              created_ = input.readInt64();
              bitField0_ |= 0x00000400;
              break;
            } // case 88
            case 101: {
              chance_ = input.readFloat();
              bitField0_ |= 0x00000800;
              break;
            } // case 101
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object customerId_ = "";
    /**
     * <code>string customerId = 1;</code>
     * @return The customerId.
     */
    public java.lang.String getCustomerId() {
      java.lang.Object ref = customerId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        customerId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string customerId = 1;</code>
     * @return The bytes for customerId.
     */
    public com.google.protobuf.ByteString
        getCustomerIdBytes() {
      java.lang.Object ref = customerId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        customerId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string customerId = 1;</code>
     * @param value The customerId to set.
     * @return This builder for chaining.
     */
    public Builder setCustomerId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      customerId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>string customerId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearCustomerId() {
      customerId_ = getDefaultInstance().getCustomerId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>string customerId = 1;</code>
     * @param value The bytes for customerId to set.
     * @return This builder for chaining.
     */
    public Builder setCustomerIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      customerId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private java.lang.Object handle_ = "";
    /**
     * <code>string handle = 2;</code>
     * @return The handle.
     */
    public java.lang.String getHandle() {
      java.lang.Object ref = handle_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        handle_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string handle = 2;</code>
     * @return The bytes for handle.
     */
    public com.google.protobuf.ByteString
        getHandleBytes() {
      java.lang.Object ref = handle_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        handle_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string handle = 2;</code>
     * @param value The handle to set.
     * @return This builder for chaining.
     */
    public Builder setHandle(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      handle_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>string handle = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearHandle() {
      handle_ = getDefaultInstance().getHandle();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <code>string handle = 2;</code>
     * @param value The bytes for handle to set.
     * @return This builder for chaining.
     */
    public Builder setHandleBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      handle_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private java.lang.Object content_ = "";
    /**
     * <code>string content = 3;</code>
     * @return The content.
     */
    public java.lang.String getContent() {
      java.lang.Object ref = content_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        content_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string content = 3;</code>
     * @return The bytes for content.
     */
    public com.google.protobuf.ByteString
        getContentBytes() {
      java.lang.Object ref = content_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        content_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string content = 3;</code>
     * @param value The content to set.
     * @return This builder for chaining.
     */
    public Builder setContent(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      content_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>string content = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearContent() {
      content_ = getDefaultInstance().getContent();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>string content = 3;</code>
     * @param value The bytes for content to set.
     * @return This builder for chaining.
     */
    public Builder setContentBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      content_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private com.google.protobuf.LazyStringArrayList medias_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    private void ensureMediasIsMutable() {
      if (!medias_.isModifiable()) {
        medias_ = new com.google.protobuf.LazyStringArrayList(medias_);
      }
      bitField0_ |= 0x00000008;
    }
    /**
     * <code>repeated string medias = 4;</code>
     * @return A list containing the medias.
     */
    public com.google.protobuf.ProtocolStringList
        getMediasList() {
      medias_.makeImmutable();
      return medias_;
    }
    /**
     * <code>repeated string medias = 4;</code>
     * @return The count of medias.
     */
    public int getMediasCount() {
      return medias_.size();
    }
    /**
     * <code>repeated string medias = 4;</code>
     * @param index The index of the element to return.
     * @return The medias at the given index.
     */
    public java.lang.String getMedias(int index) {
      return medias_.get(index);
    }
    /**
     * <code>repeated string medias = 4;</code>
     * @param index The index of the value to return.
     * @return The bytes of the medias at the given index.
     */
    public com.google.protobuf.ByteString
        getMediasBytes(int index) {
      return medias_.getByteString(index);
    }
    /**
     * <code>repeated string medias = 4;</code>
     * @param index The index to set the value at.
     * @param value The medias to set.
     * @return This builder for chaining.
     */
    public Builder setMedias(
        int index, java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ensureMediasIsMutable();
      medias_.set(index, value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string medias = 4;</code>
     * @param value The medias to add.
     * @return This builder for chaining.
     */
    public Builder addMedias(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ensureMediasIsMutable();
      medias_.add(value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string medias = 4;</code>
     * @param values The medias to add.
     * @return This builder for chaining.
     */
    public Builder addAllMedias(
        java.lang.Iterable<java.lang.String> values) {
      ensureMediasIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, medias_);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string medias = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearMedias() {
      medias_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
      bitField0_ = (bitField0_ & ~0x00000008);;
      onChanged();
      return this;
    }
    /**
     * <code>repeated string medias = 4;</code>
     * @param value The bytes of the medias to add.
     * @return This builder for chaining.
     */
    public Builder addMediasBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      ensureMediasIsMutable();
      medias_.add(value);
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private long slot_ ;
    /**
     * <code>int64 slot = 5;</code>
     * @return The slot.
     */
    @java.lang.Override
    public long getSlot() {
      return slot_;
    }
    /**
     * <code>int64 slot = 5;</code>
     * @param value The slot to set.
     * @return This builder for chaining.
     */
    public Builder setSlot(long value) {

      slot_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>int64 slot = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearSlot() {
      bitField0_ = (bitField0_ & ~0x00000010);
      slot_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object status_ = "";
    /**
     * <code>string status = 6;</code>
     * @return The status.
     */
    public java.lang.String getStatus() {
      java.lang.Object ref = status_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        status_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string status = 6;</code>
     * @return The bytes for status.
     */
    public com.google.protobuf.ByteString
        getStatusBytes() {
      java.lang.Object ref = status_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        status_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string status = 6;</code>
     * @param value The status to set.
     * @return This builder for chaining.
     */
    public Builder setStatus(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      status_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>string status = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearStatus() {
      status_ = getDefaultInstance().getStatus();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <code>string status = 6;</code>
     * @param value The bytes for status to set.
     * @return This builder for chaining.
     */
    public Builder setStatusBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      status_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    private java.lang.Object volume_ = "";
    /**
     * <code>string volume = 7;</code>
     * @return The volume.
     */
    public java.lang.String getVolume() {
      java.lang.Object ref = volume_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        volume_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string volume = 7;</code>
     * @return The bytes for volume.
     */
    public com.google.protobuf.ByteString
        getVolumeBytes() {
      java.lang.Object ref = volume_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        volume_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string volume = 7;</code>
     * @param value The volume to set.
     * @return This builder for chaining.
     */
    public Builder setVolume(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      volume_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <code>string volume = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearVolume() {
      volume_ = getDefaultInstance().getVolume();
      bitField0_ = (bitField0_ & ~0x00000040);
      onChanged();
      return this;
    }
    /**
     * <code>string volume = 7;</code>
     * @param value The bytes for volume to set.
     * @return This builder for chaining.
     */
    public Builder setVolumeBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      volume_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }

    private java.lang.Object myVote_ = "";
    /**
     * <code>string myVote = 8;</code>
     * @return The myVote.
     */
    public java.lang.String getMyVote() {
      java.lang.Object ref = myVote_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        myVote_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string myVote = 8;</code>
     * @return The bytes for myVote.
     */
    public com.google.protobuf.ByteString
        getMyVoteBytes() {
      java.lang.Object ref = myVote_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        myVote_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string myVote = 8;</code>
     * @param value The myVote to set.
     * @return This builder for chaining.
     */
    public Builder setMyVote(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      myVote_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>string myVote = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearMyVote() {
      myVote_ = getDefaultInstance().getMyVote();
      bitField0_ = (bitField0_ & ~0x00000080);
      onChanged();
      return this;
    }
    /**
     * <code>string myVote = 8;</code>
     * @param value The bytes for myVote to set.
     * @return This builder for chaining.
     */
    public Builder setMyVoteBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      myVote_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }

    private java.lang.Object vote_ = "";
    /**
     * <code>string vote = 9;</code>
     * @return The vote.
     */
    public java.lang.String getVote() {
      java.lang.Object ref = vote_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        vote_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string vote = 9;</code>
     * @return The bytes for vote.
     */
    public com.google.protobuf.ByteString
        getVoteBytes() {
      java.lang.Object ref = vote_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        vote_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string vote = 9;</code>
     * @param value The vote to set.
     * @return This builder for chaining.
     */
    public Builder setVote(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      vote_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>string vote = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearVote() {
      vote_ = getDefaultInstance().getVote();
      bitField0_ = (bitField0_ & ~0x00000100);
      onChanged();
      return this;
    }
    /**
     * <code>string vote = 9;</code>
     * @param value The bytes for vote to set.
     * @return This builder for chaining.
     */
    public Builder setVoteBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      vote_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }

    private java.lang.Object nickName_ = "";
    /**
     * <code>string nickName = 10;</code>
     * @return The nickName.
     */
    public java.lang.String getNickName() {
      java.lang.Object ref = nickName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        nickName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string nickName = 10;</code>
     * @return The bytes for nickName.
     */
    public com.google.protobuf.ByteString
        getNickNameBytes() {
      java.lang.Object ref = nickName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        nickName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string nickName = 10;</code>
     * @param value The nickName to set.
     * @return This builder for chaining.
     */
    public Builder setNickName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      nickName_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <code>string nickName = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearNickName() {
      nickName_ = getDefaultInstance().getNickName();
      bitField0_ = (bitField0_ & ~0x00000200);
      onChanged();
      return this;
    }
    /**
     * <code>string nickName = 10;</code>
     * @param value The bytes for nickName to set.
     * @return This builder for chaining.
     */
    public Builder setNickNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      nickName_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }

    private long created_ ;
    /**
     * <code>int64 created = 11;</code>
     * @return The created.
     */
    @java.lang.Override
    public long getCreated() {
      return created_;
    }
    /**
     * <code>int64 created = 11;</code>
     * @param value The created to set.
     * @return This builder for chaining.
     */
    public Builder setCreated(long value) {

      created_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>int64 created = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearCreated() {
      bitField0_ = (bitField0_ & ~0x00000400);
      created_ = 0L;
      onChanged();
      return this;
    }

    private float chance_ ;
    /**
     * <code>float chance = 12;</code>
     * @return The chance.
     */
    @java.lang.Override
    public float getChance() {
      return chance_;
    }
    /**
     * <code>float chance = 12;</code>
     * @param value The chance to set.
     * @return This builder for chaining.
     */
    public Builder setChance(float value) {

      chance_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <code>float chance = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearChance() {
      bitField0_ = (bitField0_ & ~0x00000800);
      chance_ = 0F;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.zeek.core.api.PredictionOutComesDTO)
  }

  // @@protoc_insertion_point(class_scope:com.zeek.core.api.PredictionOutComesDTO)
  private static final com.zeek.core.api.PredictionOutComesDTO DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.zeek.core.api.PredictionOutComesDTO();
  }

  public static com.zeek.core.api.PredictionOutComesDTO getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PredictionOutComesDTO>
      PARSER = new com.google.protobuf.AbstractParser<PredictionOutComesDTO>() {
    @java.lang.Override
    public PredictionOutComesDTO parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PredictionOutComesDTO> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PredictionOutComesDTO> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.zeek.core.api.PredictionOutComesDTO getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

