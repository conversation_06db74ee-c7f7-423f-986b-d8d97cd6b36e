// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: RemotePredictionService.proto

package com.zeek.core.api;

public interface CreatMarketRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.zeek.core.api.CreatMarketRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string customerId = 1;</code>
   * @return The customerId.
   */
  java.lang.String getCustomerId();
  /**
   * <code>string customerId = 1;</code>
   * @return The bytes for customerId.
   */
  com.google.protobuf.ByteString
      getCustomerIdBytes();

  /**
   * <code>string title = 2;</code>
   * @return The title.
   */
  java.lang.String getTitle();
  /**
   * <code>string title = 2;</code>
   * @return The bytes for title.
   */
  com.google.protobuf.ByteString
      getTitleBytes();

  /**
   * <code>string content = 3;</code>
   * @return The content.
   */
  java.lang.String getContent();
  /**
   * <code>string content = 3;</code>
   * @return The bytes for content.
   */
  com.google.protobuf.ByteString
      getContentBytes();

  /**
   * <code>repeated string medias = 4;</code>
   * @return A list containing the medias.
   */
  java.util.List<java.lang.String>
      getMediasList();
  /**
   * <code>repeated string medias = 4;</code>
   * @return The count of medias.
   */
  int getMediasCount();
  /**
   * <code>repeated string medias = 4;</code>
   * @param index The index of the element to return.
   * @return The medias at the given index.
   */
  java.lang.String getMedias(int index);
  /**
   * <code>repeated string medias = 4;</code>
   * @param index The index of the value to return.
   * @return The bytes of the medias at the given index.
   */
  com.google.protobuf.ByteString
      getMediasBytes(int index);

  /**
   * <code>string token = 5;</code>
   * @return The token.
   */
  java.lang.String getToken();
  /**
   * <code>string token = 5;</code>
   * @return The bytes for token.
   */
  com.google.protobuf.ByteString
      getTokenBytes();

  /**
   * <code>int64 slot = 6;</code>
   * @return The slot.
   */
  long getSlot();

  /**
   * <code>string value = 7;</code>
   * @return The value.
   */
  java.lang.String getValue();
  /**
   * <code>string value = 7;</code>
   * @return The bytes for value.
   */
  com.google.protobuf.ByteString
      getValueBytes();

  /**
   * <code>int64 endTime = 8;</code>
   * @return The endTime.
   */
  long getEndTime();

  /**
   * <code>string oracleType = 9;</code>
   * @return The oracleType.
   */
  java.lang.String getOracleType();
  /**
   * <code>string oracleType = 9;</code>
   * @return The bytes for oracleType.
   */
  com.google.protobuf.ByteString
      getOracleTypeBytes();
}
