// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: RemotePredictionService.proto

package com.zeek.core.api;

public final class RemotePredictionServiceOuterClass {
  private RemotePredictionServiceOuterClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zeek_core_api_PredictionConfigResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zeek_core_api_PredictionConfigResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zeek_core_api_PredictionConfigDTO_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zeek_core_api_PredictionConfigDTO_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zeek_core_api_PredictionTokenConfig_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zeek_core_api_PredictionTokenConfig_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zeek_core_api_PredictionDaysConfig_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zeek_core_api_PredictionDaysConfig_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zeek_core_api_PredictionFeeConfig_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zeek_core_api_PredictionFeeConfig_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zeek_core_api_PredictionVoteConfig_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zeek_core_api_PredictionVoteConfig_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zeek_core_api_PredictionOutcomesRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zeek_core_api_PredictionOutcomesRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zeek_core_api_PredictionMarketListDTO_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zeek_core_api_PredictionMarketListDTO_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zeek_core_api_PredictionMarketDTO_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zeek_core_api_PredictionMarketDTO_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zeek_core_api_PredictionOutComesDTO_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zeek_core_api_PredictionOutComesDTO_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zeek_core_api_CreatMarketRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zeek_core_api_CreatMarketRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zeek_core_api_MarketDetailRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zeek_core_api_MarketDetailRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zeek_core_api_MarketsRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zeek_core_api_MarketsRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zeek_core_api_MarketPriceRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zeek_core_api_MarketPriceRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zeek_core_api_MarketPriceDTO_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zeek_core_api_MarketPriceDTO_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zeek_core_api_TradeRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zeek_core_api_TradeRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zeek_core_api_TradeResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zeek_core_api_TradeResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zeek_core_api_ParticipateRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zeek_core_api_ParticipateRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zeek_core_api_HasParticipatedRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zeek_core_api_HasParticipatedRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zeek_core_api_ParticipatedMarketsRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zeek_core_api_ParticipatedMarketsRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zeek_core_api_VoteRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zeek_core_api_VoteRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zeek_core_api_VoteResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zeek_core_api_VoteResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zeek_core_api_PositionsRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zeek_core_api_PositionsRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zeek_core_api_MarketPositionsDTO_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zeek_core_api_MarketPositionsDTO_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zeek_core_api_CloseMarketRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zeek_core_api_CloseMarketRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_zeek_core_api_CloseMarketResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_zeek_core_api_CloseMarketResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\035RemotePredictionService.proto\022\021com.zee" +
      "k.core.api\032\nBase.proto\032\033google/protobuf/" +
      "empty.proto\"r\n\030PredictionConfigResponse\022" +
      "\014\n\004code\030\001 \001(\t\022\017\n\007message\030\002 \001(\t\0227\n\007config" +
      "s\030\003 \001(\0132&.com.zeek.core.api.PredictionCo" +
      "nfigDTO\"\362\001\n\023PredictionConfigDTO\0228\n\006token" +
      "s\030\001 \003(\0132(.com.zeek.core.api.PredictionTo" +
      "kenConfig\0225\n\004days\030\002 \001(\0132\'.com.zeek.core." +
      "api.PredictionDaysConfig\0223\n\003fee\030\003 \001(\0132&." +
      "com.zeek.core.api.PredictionFeeConfig\0225\n" +
      "\004vote\030\004 \001(\0132\'.com.zeek.core.api.Predicti" +
      "onVoteConfig\"\206\001\n\025PredictionTokenConfig\022\014" +
      "\n\004name\030\001 \001(\t\022\016\n\006symbol\030\002 \001(\t\022\017\n\007address\030" +
      "\003 \001(\t\022\025\n\rcollateralMin\030\004 \001(\t\022\025\n\rcollater" +
      "alMax\030\005 \001(\t\022\020\n\010slotUnit\030\006 \001(\t\"0\n\024Predict" +
      "ionDaysConfig\022\013\n\003min\030\001 \001(\005\022\013\n\003max\030\002 \001(\005\"" +
      "V\n\023PredictionFeeConfig\022\r\n\005total\030\001 \001(\005\022\020\n" +
      "\010platform\030\002 \001(\005\022\016\n\006poster\030\003 \001(\005\022\016\n\006answe" +
      "r\030\004 \001(\005\"A\n\024PredictionVoteConfig\022\013\n\003min\030\001" +
      " \001(\005\022\013\n\003max\030\002 \001(\005\022\017\n\007options\030\003 \003(\005\"p\n\031Pr" +
      "edictionOutcomesRequest\022\022\n\ncustomerId\030\001 " +
      "\001(\t\022\020\n\010marketId\030\002 \001(\t\022\r\n\005token\030\003 \001(\t\022\r\n\005" +
      "value\030\004 \001(\t\022\017\n\007content\030\005 \001(\t\"\205\001\n\027Predict" +
      "ionMarketListDTO\022\026\n\tnextToken\030\001 \001(\tH\000\210\001\001" +
      "\022D\n\024predictionMarketDTOs\030\002 \003(\0132&.com.zee" +
      "k.core.api.PredictionMarketDTOB\014\n\n_nextT" +
      "oken\"\326\004\n\023PredictionMarketDTO\022\023\n\013conditio" +
      "nId\030\001 \001(\t\022\022\n\ncustomerId\030\002 \001(\t\022\017\n\007chainId" +
      "\030\003 \001(\003\022\022\n\nquestionId\030\004 \001(\t\022\025\n\rmarketAddr" +
      "ess\030\005 \001(\t\022\r\n\005title\030\006 \001(\t\022\017\n\007content\030\007 \001(" +
      "\t\022\016\n\006medias\030\010 \003(\t\022\r\n\005token\030\t \001(\t\022\r\n\005valu" +
      "e\030\n \001(\t\022\014\n\004slot\030\013 \001(\t\022\016\n\006volume\030\014 \001(\t\022\021\n" +
      "\tvolumeUsd\030\r \001(\001\022\014\n\004vote\030\016 \001(\003\022\017\n\007endTim" +
      "e\030\017 \001(\003\022\017\n\007created\030\020 \001(\003\022\020\n\010modified\030\021 \001" +
      "(\003\022:\n\010outcomes\030\022 \003(\0132(.com.zeek.core.api" +
      ".PredictionOutComesDTO\022\013\n\003fee\030\023 \001(\003\022\020\n\010o" +
      "wnerFee\030\024 \001(\003\022\023\n\013proposalFee\030\025 \001(\003\022\016\n\006st" +
      "atus\030\026 \001(\t\022\026\n\016hasParticipant\030\027 \001(\010\022\024\n\014po" +
      "sterHandle\030\030 \001(\t\022\020\n\010nickName\030\031 \001(\t\022\016\n\006av" +
      "atar\030\032 \001(\t\022\020\n\010winPoint\030\033 \001(\t\022\023\n\013claimSta" +
      "tus\030\034 \001(\t\022\016\n\006result\030\035 \003(\003\022\022\n\nclaimValue\030" +
      "\036 \001(\t\"\333\001\n\025PredictionOutComesDTO\022\022\n\ncusto" +
      "merId\030\001 \001(\t\022\016\n\006handle\030\002 \001(\t\022\017\n\007content\030\003" +
      " \001(\t\022\016\n\006medias\030\004 \003(\t\022\014\n\004slot\030\005 \001(\003\022\016\n\006st" +
      "atus\030\006 \001(\t\022\016\n\006volume\030\007 \001(\t\022\016\n\006myVote\030\010 \001" +
      "(\t\022\014\n\004vote\030\t \001(\t\022\020\n\010nickName\030\n \001(\t\022\017\n\007cr" +
      "eated\030\013 \001(\003\022\016\n\006chance\030\014 \001(\002\"\251\001\n\022CreatMar" +
      "ketRequest\022\022\n\ncustomerId\030\001 \001(\t\022\r\n\005title\030" +
      "\002 \001(\t\022\017\n\007content\030\003 \001(\t\022\016\n\006medias\030\004 \003(\t\022\r" +
      "\n\005token\030\005 \001(\t\022\014\n\004slot\030\006 \001(\003\022\r\n\005value\030\007 \001" +
      "(\t\022\017\n\007endTime\030\010 \001(\003\022\022\n\noracleType\030\t \001(\t\"" +
      ";\n\023MarketDetailRequest\022\020\n\010marketId\030\001 \001(\t" +
      "\022\022\n\ncustomerId\030\002 \001(\t\"\207\001\n\016MarketsRequest\022" +
      "/\n\004type\030\001 \001(\0162!.com.zeek.core.api.Market" +
      "ListType\022\022\n\ncustomerId\030\002 \001(\t\022\021\n\tnextToke" +
      "n\030\003 \001(\t\022\r\n\005limit\030\004 \001(\005\022\016\n\006status\030\005 \001(\t\"4" +
      "\n\022MarketPriceRequest\022\020\n\010marketId\030\001 \001(\t\022\014" +
      "\n\004slot\030\002 \001(\005\"-\n\016MarketPriceDTO\022\014\n\004slot\030\001" +
      " \001(\005\022\r\n\005price\030\002 \001(\t\"i\n\014TradeRequest\022\020\n\010m" +
      "arketId\030\001 \001(\t\022\014\n\004slot\030\002 \001(\005\022\016\n\006amount\030\003 " +
      "\001(\t\022\025\n\rneedSignature\030\004 \001(\010\022\022\n\ncustomerId" +
      "\030\005 \001(\t\"\246\001\n\rTradeResponse\022\020\n\010balances\030\001 \003" +
      "(\t\022\016\n\006amount\030\002 \001(\t\022\017\n\007funding\030\003 \001(\t\022\021\n\ts" +
      "lotCount\030\004 \001(\005\022\014\n\004slot\030\005 \001(\005\022\017\n\007netCost\030" +
      "\006 \001(\t\022\013\n\003fee\030\007 \001(\t\022\021\n\tsignature\030\010 \001(\t\022\020\n" +
      "\010avgPrice\030\t \001(\t\"J\n\022ParticipateRequest\022\022\n" +
      "\ncustomerId\030\001 \001(\t\022\020\n\010marketId\030\002 \001(\t\022\016\n\006a" +
      "ction\030\003 \001(\t\"N\n\026HasParticipatedRequest\022\022\n" +
      "\ncustomerId\030\001 \001(\t\022\020\n\010marketId\030\002 \001(\t\022\016\n\006a" +
      "ction\030\003 \001(\t\"R\n\032ParticipatedMarketsReques" +
      "t\022\022\n\ncustomerId\030\001 \001(\t\022\021\n\tnextToken\030\002 \001(\t" +
      "\022\r\n\005limit\030\003 \001(\005\"Q\n\013VoteRequest\022\020\n\010market" +
      "Id\030\001 \001(\t\022\014\n\004slot\030\002 \001(\005\022\016\n\006amount\030\003 \001(\003\022\022" +
      "\n\ncustomerId\030\004 \001(\t\"=\n\014VoteResponse\022\016\n\006am" +
      "ount\030\001 \001(\003\022\014\n\004slot\030\002 \001(\005\022\017\n\007success\030\003 \001(" +
      "\010\"F\n\020PositionsRequest\022\022\n\ncustomerId\030\001 \001(" +
      "\t\022\020\n\010marketId\030\002 \001(\t\022\014\n\004slot\030\003 \001(\005\"C\n\022Mar" +
      "ketPositionsDTO\022\014\n\004slot\030\001 \001(\005\022\020\n\010positio" +
      "n\030\002 \001(\t\022\r\n\005limit\030\003 \001(\t\"4\n\022CloseMarketReq" +
      "uest\022\020\n\010marketId\030\001 \001(\t\022\014\n\004slot\030\002 \001(\005\"K\n\023" +
      "CloseMarketResponse\022\020\n\010marketId\030\001 \001(\t\022\017\n" +
      "\007payouts\030\002 \003(\t\022\021\n\tsignature\030\003 \001(\t**\n\016Mar" +
      "ketListType\022\007\n\003All\020\000\022\017\n\013Participant\020\0012\365\010" +
      "\n\027RemotePredictionService\022V\n\014createMarke" +
      "t\022%.com.zeek.core.api.CreatMarketRequest" +
      "\032\037.com.zeek.core.api.CommonResult\022W\n\014Mar" +
      "ketDetail\022&.com.zeek.core.api.MarketDeta" +
      "ilRequest\032\037.com.zeek.core.api.CommonResu" +
      "lt\022M\n\007Markets\022!.com.zeek.core.api.Market" +
      "sRequest\032\037.com.zeek.core.api.CommonResul" +
      "t\022^\n\rcreateOutcome\022,.com.zeek.core.api.P" +
      "redictionOutcomesRequest\032\037.com.zeek.core" +
      ".api.CommonResult\022O\n\005price\022%.com.zeek.co" +
      "re.api.MarketPriceRequest\032\037.com.zeek.cor" +
      "e.api.CommonResult\022I\n\005trade\022\037.com.zeek.c" +
      "ore.api.TradeRequest\032\037.com.zeek.core.api" +
      ".CommonResult\022N\n\007configs\022\026.google.protob" +
      "uf.Empty\032+.com.zeek.core.api.PredictionC" +
      "onfigResponse\022G\n\004vote\022\036.com.zeek.core.ap" +
      "i.VoteRequest\032\037.com.zeek.core.api.Common" +
      "Result\022Q\n\tpositions\022#.com.zeek.core.api." +
      "PositionsRequest\032\037.com.zeek.core.api.Com" +
      "monResult\022U\n\013closeMarket\022%.com.zeek.core" +
      ".api.CloseMarketRequest\032\037.com.zeek.core." +
      "api.CommonResult\022U\n\013participate\022%.com.ze" +
      "ek.core.api.ParticipateRequest\032\037.com.zee" +
      "k.core.api.CommonResult\022]\n\017hasParticipat" +
      "ed\022).com.zeek.core.api.HasParticipatedRe" +
      "quest\032\037.com.zeek.core.api.CommonResult\022e" +
      "\n\023participatedMarkets\022-.com.zeek.core.ap" +
      "i.ParticipatedMarketsRequest\032\037.com.zeek." +
      "core.api.CommonResultB\025\n\021com.zeek.core.a" +
      "piP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.zeek.core.api.Base.getDescriptor(),
          com.google.protobuf.EmptyProto.getDescriptor(),
        });
    internal_static_com_zeek_core_api_PredictionConfigResponse_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_zeek_core_api_PredictionConfigResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zeek_core_api_PredictionConfigResponse_descriptor,
        new java.lang.String[] { "Code", "Message", "Configs", });
    internal_static_com_zeek_core_api_PredictionConfigDTO_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_zeek_core_api_PredictionConfigDTO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zeek_core_api_PredictionConfigDTO_descriptor,
        new java.lang.String[] { "Tokens", "Days", "Fee", "Vote", });
    internal_static_com_zeek_core_api_PredictionTokenConfig_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_zeek_core_api_PredictionTokenConfig_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zeek_core_api_PredictionTokenConfig_descriptor,
        new java.lang.String[] { "Name", "Symbol", "Address", "CollateralMin", "CollateralMax", "SlotUnit", });
    internal_static_com_zeek_core_api_PredictionDaysConfig_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_zeek_core_api_PredictionDaysConfig_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zeek_core_api_PredictionDaysConfig_descriptor,
        new java.lang.String[] { "Min", "Max", });
    internal_static_com_zeek_core_api_PredictionFeeConfig_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_zeek_core_api_PredictionFeeConfig_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zeek_core_api_PredictionFeeConfig_descriptor,
        new java.lang.String[] { "Total", "Platform", "Poster", "Answer", });
    internal_static_com_zeek_core_api_PredictionVoteConfig_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_zeek_core_api_PredictionVoteConfig_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zeek_core_api_PredictionVoteConfig_descriptor,
        new java.lang.String[] { "Min", "Max", "Options", });
    internal_static_com_zeek_core_api_PredictionOutcomesRequest_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_zeek_core_api_PredictionOutcomesRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zeek_core_api_PredictionOutcomesRequest_descriptor,
        new java.lang.String[] { "CustomerId", "MarketId", "Token", "Value", "Content", });
    internal_static_com_zeek_core_api_PredictionMarketListDTO_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_zeek_core_api_PredictionMarketListDTO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zeek_core_api_PredictionMarketListDTO_descriptor,
        new java.lang.String[] { "NextToken", "PredictionMarketDTOs", "NextToken", });
    internal_static_com_zeek_core_api_PredictionMarketDTO_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_zeek_core_api_PredictionMarketDTO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zeek_core_api_PredictionMarketDTO_descriptor,
        new java.lang.String[] { "ConditionId", "CustomerId", "ChainId", "QuestionId", "MarketAddress", "Title", "Content", "Medias", "Token", "Value", "Slot", "Volume", "VolumeUsd", "Vote", "EndTime", "Created", "Modified", "Outcomes", "Fee", "OwnerFee", "ProposalFee", "Status", "HasParticipant", "PosterHandle", "NickName", "Avatar", "WinPoint", "ClaimStatus", "Result", "ClaimValue", });
    internal_static_com_zeek_core_api_PredictionOutComesDTO_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_zeek_core_api_PredictionOutComesDTO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zeek_core_api_PredictionOutComesDTO_descriptor,
        new java.lang.String[] { "CustomerId", "Handle", "Content", "Medias", "Slot", "Status", "Volume", "MyVote", "Vote", "NickName", "Created", "Chance", });
    internal_static_com_zeek_core_api_CreatMarketRequest_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_zeek_core_api_CreatMarketRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zeek_core_api_CreatMarketRequest_descriptor,
        new java.lang.String[] { "CustomerId", "Title", "Content", "Medias", "Token", "Slot", "Value", "EndTime", "OracleType", });
    internal_static_com_zeek_core_api_MarketDetailRequest_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_com_zeek_core_api_MarketDetailRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zeek_core_api_MarketDetailRequest_descriptor,
        new java.lang.String[] { "MarketId", "CustomerId", });
    internal_static_com_zeek_core_api_MarketsRequest_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_com_zeek_core_api_MarketsRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zeek_core_api_MarketsRequest_descriptor,
        new java.lang.String[] { "Type", "CustomerId", "NextToken", "Limit", "Status", });
    internal_static_com_zeek_core_api_MarketPriceRequest_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_com_zeek_core_api_MarketPriceRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zeek_core_api_MarketPriceRequest_descriptor,
        new java.lang.String[] { "MarketId", "Slot", });
    internal_static_com_zeek_core_api_MarketPriceDTO_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_com_zeek_core_api_MarketPriceDTO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zeek_core_api_MarketPriceDTO_descriptor,
        new java.lang.String[] { "Slot", "Price", });
    internal_static_com_zeek_core_api_TradeRequest_descriptor =
      getDescriptor().getMessageTypes().get(15);
    internal_static_com_zeek_core_api_TradeRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zeek_core_api_TradeRequest_descriptor,
        new java.lang.String[] { "MarketId", "Slot", "Amount", "NeedSignature", "CustomerId", });
    internal_static_com_zeek_core_api_TradeResponse_descriptor =
      getDescriptor().getMessageTypes().get(16);
    internal_static_com_zeek_core_api_TradeResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zeek_core_api_TradeResponse_descriptor,
        new java.lang.String[] { "Balances", "Amount", "Funding", "SlotCount", "Slot", "NetCost", "Fee", "Signature", "AvgPrice", });
    internal_static_com_zeek_core_api_ParticipateRequest_descriptor =
      getDescriptor().getMessageTypes().get(17);
    internal_static_com_zeek_core_api_ParticipateRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zeek_core_api_ParticipateRequest_descriptor,
        new java.lang.String[] { "CustomerId", "MarketId", "Action", });
    internal_static_com_zeek_core_api_HasParticipatedRequest_descriptor =
      getDescriptor().getMessageTypes().get(18);
    internal_static_com_zeek_core_api_HasParticipatedRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zeek_core_api_HasParticipatedRequest_descriptor,
        new java.lang.String[] { "CustomerId", "MarketId", "Action", });
    internal_static_com_zeek_core_api_ParticipatedMarketsRequest_descriptor =
      getDescriptor().getMessageTypes().get(19);
    internal_static_com_zeek_core_api_ParticipatedMarketsRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zeek_core_api_ParticipatedMarketsRequest_descriptor,
        new java.lang.String[] { "CustomerId", "NextToken", "Limit", });
    internal_static_com_zeek_core_api_VoteRequest_descriptor =
      getDescriptor().getMessageTypes().get(20);
    internal_static_com_zeek_core_api_VoteRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zeek_core_api_VoteRequest_descriptor,
        new java.lang.String[] { "MarketId", "Slot", "Amount", "CustomerId", });
    internal_static_com_zeek_core_api_VoteResponse_descriptor =
      getDescriptor().getMessageTypes().get(21);
    internal_static_com_zeek_core_api_VoteResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zeek_core_api_VoteResponse_descriptor,
        new java.lang.String[] { "Amount", "Slot", "Success", });
    internal_static_com_zeek_core_api_PositionsRequest_descriptor =
      getDescriptor().getMessageTypes().get(22);
    internal_static_com_zeek_core_api_PositionsRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zeek_core_api_PositionsRequest_descriptor,
        new java.lang.String[] { "CustomerId", "MarketId", "Slot", });
    internal_static_com_zeek_core_api_MarketPositionsDTO_descriptor =
      getDescriptor().getMessageTypes().get(23);
    internal_static_com_zeek_core_api_MarketPositionsDTO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zeek_core_api_MarketPositionsDTO_descriptor,
        new java.lang.String[] { "Slot", "Position", "Limit", });
    internal_static_com_zeek_core_api_CloseMarketRequest_descriptor =
      getDescriptor().getMessageTypes().get(24);
    internal_static_com_zeek_core_api_CloseMarketRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zeek_core_api_CloseMarketRequest_descriptor,
        new java.lang.String[] { "MarketId", "Slot", });
    internal_static_com_zeek_core_api_CloseMarketResponse_descriptor =
      getDescriptor().getMessageTypes().get(25);
    internal_static_com_zeek_core_api_CloseMarketResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_zeek_core_api_CloseMarketResponse_descriptor,
        new java.lang.String[] { "MarketId", "Payouts", "Signature", });
    com.zeek.core.api.Base.getDescriptor();
    com.google.protobuf.EmptyProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
