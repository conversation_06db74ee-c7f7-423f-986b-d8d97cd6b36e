syntax = "proto3";

package com.zeek.core.api;
option java_package = "com.zeek.core.api";
option java_multiple_files = true;
import "Base.proto"; // 导入 Google Timestamp 类型
import "google/protobuf/empty.proto";

message PredictionConfigResponse {
  string code = 1;
  string message = 2;
  PredictionConfigDTO configs = 3;
}

message PredictionConfigDTO {
  repeated PredictionTokenConfig tokens = 1;
  PredictionDaysConfig days = 2;
  PredictionFeeConfig fee = 3;
  PredictionVoteConfig vote = 4;
}

message PredictionTokenConfig {
  string name = 1;
  string symbol = 2;
  string address = 3;
  string collateralMin = 4;
  string collateralMax = 5;
  string slotUnit = 6;
}

message PredictionDaysConfig {
  int32 min = 1;
  int32 max = 2;
}

message PredictionFeeConfig {
  int32 total = 1;
  int32 platform = 2;
  int32 poster = 3;
  int32 answer = 4;
}

message PredictionVoteConfig {
  int32 min = 1;
  int32 max = 2;
  repeated int32 options = 3;
}

message PredictionOutcomesRequest {
  string customerId = 1;
  string marketId = 2;
  string token = 3;
  string value = 4;
  string content = 5;
}

message PredictionMarketListDTO {
  optional string nextToken = 1;
  repeated PredictionMarketDTO predictionMarketDTOs = 2;
}

message PredictionMarketDTO {
  string conditionId=1;
  string customerId=2;
  int64 chainId=3;
  string questionId=4;
  string marketAddress=5;
  string title=6;
  string content=7;
  repeated string medias=8;
  string token=9;
  string value=10;
  string slot=11;
  string volume=12;
  double volumeUsd=13;
  int64 vote=14;
  int64 endTime=15;
  int64 created=16;
  int64 modified=17;
  repeated PredictionOutComesDTO outcomes=18;
  int64 fee=19;
  int64 ownerFee=20;
  int64 proposalFee=21;
  string status=22;
  bool hasParticipant=23;
  string posterHandle=24;
  string nickName=25;
  string avatar=26;
  string winPoint=27;
  string claimStatus=28;
  repeated int64 result=29;
  string claimValue=30;
}

message PredictionOutComesDTO {
  string customerId=1;
  string handle=2;
  string content=3;
  repeated string medias=4;
  int64 slot=5;
  string status=6;
  string volume=7;
  string myVote=8;
  string vote=9;
  string nickName=10;
  int64 created=11;
  float chance=12;
}

enum MarketListType {
  All = 0;
  Participant = 1;
}

message CreatMarketRequest {
  string customerId = 1;
  string title = 2;
  string content = 3;
  repeated string medias = 4;
  string token = 5;
  int64 slot = 6;
  string value = 7;
  int64 endTime = 8;
}

message MarketDetailRequest {
  string marketId = 1;
  string customerId = 2;
}
message MarketsRequest {
  MarketListType type = 1;
  string customerId = 2;
  string nextToken = 3;
  int32 limit = 4;
  string status = 5;
}

message MarketPriceRequest {
  string marketId = 1;
  int32 slot = 2;
}

message MarketPriceDTO {
  int32 slot = 1;
  string price = 2;
}

message TradeRequest {
  string marketId = 1;
  int32 slot = 2;
  string amount = 3;
  bool needSignature = 4;
  string customerId = 5;
}

message TradeResponse {
  repeated string balances = 1;
  string amount = 2;
  string funding = 3;
  int32 slotCount = 4;
  int32 slot = 5;
  string netCost = 6; // 包含手续费
  string fee = 7; // 手续费
  string signature = 8;
  string avgPrice = 9;
}

message ParticipateRequest {
  string customerId = 1;
  string marketId = 2;
  string action = 3;
}

message HasParticipatedRequest {
  string customerId = 1;
  string marketId = 2;
  string action = 3;
}

message ParticipatedMarketsRequest {
  string customerId = 1;
  string nextToken = 2;
  int32 limit = 3;
}

message VoteRequest {
  string marketId = 1;
  int32 slot = 2;
  int64 amount = 3;
  string customerId = 4;
}

message VoteResponse {
  int64 amount = 1;
  int32 slot = 2;
  bool success = 3;
}

message PositionsRequest {
  string customerId = 1;
  string marketId = 2;
  int32 slot = 3;
}

message MarketPositionsDTO {
  int32 slot = 1;
  string position = 2;
  string limit = 3;
}

message CloseMarketRequest {
  string marketId = 1;
}

message CloseMarketResponse {
  string marketId = 1;
  repeated string payouts = 2;
  string signature = 3;

}

service RemotePredictionService {
  rpc createMarket(CreatMarketRequest) returns (CommonResult);
  rpc MarketDetail(MarketDetailRequest) returns (CommonResult);
  rpc Markets(MarketsRequest) returns (CommonResult);
  rpc createOutcome(PredictionOutcomesRequest) returns (CommonResult);
  rpc price(MarketPriceRequest) returns (CommonResult);
  rpc trade(TradeRequest) returns (CommonResult);
  rpc configs(google.protobuf.Empty) returns (PredictionConfigResponse);
  rpc vote(VoteRequest) returns (CommonResult);
  rpc positions(PositionsRequest) returns (CommonResult);
  rpc closeMarket(CloseMarketRequest) returns (CommonResult);

  // 市场参与相关接口
  rpc participate(ParticipateRequest) returns (CommonResult);
  rpc hasParticipated(HasParticipatedRequest) returns (CommonResult);
  rpc participatedMarkets(ParticipatedMarketsRequest) returns (CommonResult);
}

