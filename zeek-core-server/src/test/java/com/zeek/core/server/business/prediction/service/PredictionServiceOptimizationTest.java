package com.zeek.core.server.business.prediction.service;

import com.zeek.core.common.exception.BaseException;
import com.zeek.core.server.business.prediction.service.impl.PredictionVoteServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.math.BigInteger;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 预测服务优化测试
 * 验证立即可做的改进是否正确实现
 */
@ExtendWith(MockitoExtension.class)
public class PredictionServiceOptimizationTest {

    @Mock
    private RedisTemplate<String, String> redisTemplate;

    @Mock
    private ValueOperations<String, String> valueOperations;

    @InjectMocks
    private PredictionVoteServiceImpl predictionVoteService;

    /**
     * 测试投票结果缓存逻辑是否正确移到PredictionVoteService
     */
    @Test
    public void testVoteResultCacheLogicMoved() {
        // 准备测试数据
        String marketId = "test-market-id";
        
        // Mock Redis操作
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        when(valueOperations.get(anyString())).thenReturn(null);

        // 测试获取缓存结果
        List<BigInteger> result = predictionVoteService.getCachedVoteResult(marketId);
        
        // 验证结果
        assertNull(result, "当缓存中没有数据时应返回null");
    }

    /**
     * 测试参数校验是否正确
     */
    @Test
    public void testParameterValidation() {
        // 测试空的marketId
        List<BigInteger> result = predictionVoteService.getCachedVoteResult("");
        assertNull(result, "空的marketId应返回null");
        
        result = predictionVoteService.getCachedVoteResult(null);
        assertNull(result, "null的marketId应返回null");
    }

    /**
     * 测试异常处理是否统一
     */
    @Test
    public void testUnifiedExceptionHandling() {
        // 测试无效参数异常
        assertThrows(BaseException.class, () -> {
            predictionVoteService.calculateAndCacheVoteResult("", -1);
        }, "无效参数应抛出BaseException");
        
        assertThrows(BaseException.class, () -> {
            predictionVoteService.calculateAndCacheVoteResult(null, 0);
        }, "null参数应抛出BaseException");
    }
}
