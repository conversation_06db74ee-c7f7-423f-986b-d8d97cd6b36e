package com.zeek.core.server.common;

import com.zeek.core.common.constants.ZeekConstants;
import com.zeek.core.contract.abi.LMSRMarket;
import org.junit.Test;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.http.HttpService;
import org.web3j.tx.ReadonlyTransactionManager;

import java.math.BigInteger;

public class LMSRMarketTest {

    @Test
    public void test() throws Exception {
        Web3j web3j = Web3j.build(new HttpService("https://berachain-bepolia.g.alchemy.com/v2/1cRCJvwOUL8ik1-MeqezYd_Z_AaHS9tn"));

        String ca = "0xcbdc9d1ee14e6ba7bc9b543fe943eadc43c62ad6";
        int slot = 0;

        LMSRMarket lmsrMarket = LMSRMarket.load(ca, web3j, new ReadonlyTransactionManager(web3j, ZeekConstants.ZEROADDRESS), null);
        BigInteger positionId = lmsrMarket.generateAtomicPositionId(BigInteger.valueOf(slot)).send();
        System.out.println(positionId);
    }

}