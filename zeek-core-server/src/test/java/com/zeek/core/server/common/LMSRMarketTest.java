package com.zeek.core.server.common;

import com.zeek.core.common.constants.ZeekConstants;
import com.zeek.core.contract.abi.ConditionalTokens;
import com.zeek.core.contract.abi.LMSRMarket;
import com.zeek.core.contract.utils.HexUtils;
import org.junit.Test;
import org.web3j.abi.datatypes.generated.Bytes32;
import org.web3j.crypto.ContractUtils;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.http.HttpService;
import org.web3j.tx.ReadonlyTransactionManager;
import org.web3j.utils.Numeric;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;

import static com.zeek.core.server.common.ConstructorDataEncoder.encodeConstructorData;

public class LMSRMarketTest {

    @Test
    public void test() throws Exception {
        Web3j web3j = Web3j.build(new HttpService("https://berachain-bepolia.g.alchemy.com/v2/1cRCJvwOUL8ik1-MeqezYd_Z_AaHS9tn"));

        String ca = "******************************************";
        int slot = 0;

        LMSRMarket lmsrMarket = LMSRMarket.load(ca, web3j, new ReadonlyTransactionManager(web3j, ZeekConstants.ZEROADDRESS), null);
        BigInteger positionId = lmsrMarket.generateAtomicPositionId(BigInteger.valueOf(slot)).send();
        System.out.println(positionId);
        String oracleAddress = lmsrMarket.oracleAddress().send();
        System.out.println(oracleAddress);
    }

    @Test
    public void fundingOf() throws Exception {
        Web3j web3j = Web3j.build(new HttpService("https://berachain-bepolia.g.alchemy.com/v2/1cRCJvwOUL8ik1-MeqezYd_Z_AaHS9tn"));

        String ca = "0x4EFb7f5E1f67365eD5F5439AEE5AAF1C666908e9";

        ConditionalTokens conditionalTokens = ConditionalTokens.load(ca, web3j, new ReadonlyTransactionManager(web3j, ZeekConstants.ZEROADDRESS), null);

        String condictionId = "0x2cfc8138bd7f89be54c5589f6d62b0ddc74c5bba0a40b212a52ccd6525204fc5";
        byte[] conditionIdBytes = condictionId.getBytes(StandardCharsets.UTF_8);
        byte[] paddedConditionIdBytes = new byte[32];
        System.arraycopy(conditionIdBytes, 0, paddedConditionIdBytes, 0, Math.min(conditionIdBytes.length, 32));

        BigInteger funding = conditionalTokens.fundingOf("0x6969696969696969696969696969696969696969", paddedConditionIdBytes).send();
        System.out.println(funding);
    }

    @Test
    public void test2() throws Exception {

        String factory  = "******************************************";
        String pmSystem = "******************************************";
        String questionId = "0x29f46b988709c4f2c9718aecf1db65426c3a51fb63cceee1a256170129b3cbef";
        BigInteger outcomeSlotCount = BigInteger.valueOf(2);
        BigInteger expiredAt = BigInteger.valueOf(1749111681); // 24小时后
        BigInteger margin = new BigInteger("2"); // 1 ETH in wei

        byte[] constructorData = encodeConstructorData(
                pmSystem,
                questionId,
                outcomeSlotCount,
                expiredAt,
                margin
        );

        byte[] slot = Numeric.toBytesPadded(BigInteger.valueOf(0), 32);
        String ca = ContractUtils.generateCreate2ContractAddress(factory, slot, constructorData);
        System.out.println(ca); // ******************************************
    }

}