package com.zeek.core.server.common;

import org.web3j.abi.FunctionEncoder;
import org.web3j.abi.datatypes.*;
import org.web3j.abi.datatypes.generated.Bytes32;
import org.web3j.abi.datatypes.generated.Uint256;
import java.math.BigInteger;
import java.util.Arrays;

public class ConstructorDataEncoder {
    
    /**
     * 编码构造函数参数
     * 
     * @param pmSystem 预测市场系统地址
     * @param questionId 问题ID (32字节)
     * @param outcomeSlotCount 结果槽数量
     * @param expiredAt 过期时间
     * @param margin 保证金
     * @return 编码后的字节数组
     */
    public static byte[] encodeConstructorData(
            String pmSystem,
            byte[] questionId,
            BigInteger outcomeSlotCount,
            BigInteger expiredAt,
            BigInteger margin) {
        
        // 创建 ABI 类型
        Address pmSystemAddress = new Address(pmSystem);
        Bytes32 questionIdBytes = new Bytes32(questionId);
        Uint256 outcomeSlotCountUint = new Uint256(outcomeSlotCount);
        Uint256 expiredAtUint = new Uint256(expiredAt);
        Uint256 marginUint = new Uint256(margin);
        
        // 使用 FunctionEncoder 进行编码
        String encodedData = FunctionEncoder.encodeConstructor(
            Arrays.asList(
                pmSystemAddress,
                questionIdBytes,
                outcomeSlotCountUint,
                expiredAtUint,
                marginUint
            )
        );
        
        // 转换为字节数组（去掉 0x 前缀）
        return org.web3j.utils.Numeric.hexStringToByteArray(encodedData);
    }
    
    /**
     * 重载方法 - 使用字符串形式的 questionId
     */
    public static byte[] encodeConstructorData(
            String pmSystem,
            String questionId,
            BigInteger outcomeSlotCount,
            BigInteger expiredAt,
            BigInteger margin) {
        
        // 将 questionId 字符串转换为 32 字节数组
        byte[] questionIdBytes = org.web3j.utils.Numeric.hexStringToByteArray(questionId);
        
        return encodeConstructorData(pmSystem, questionIdBytes, outcomeSlotCount, expiredAt, margin);
    }
    
    /**
     * 示例使用方法
     */
    public static void main(String[] args) {
        try {
            String pmSystem = "******************************************";
            String questionId = "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890";
            BigInteger outcomeSlotCount = BigInteger.valueOf(2);
            BigInteger expiredAt = BigInteger.valueOf(System.currentTimeMillis() / 1000 + 86400); // 24小时后
            BigInteger margin = new BigInteger("1000000000000000000"); // 1 ETH in wei
            
            byte[] constructorData = encodeConstructorData(
                pmSystem,
                questionId,
                outcomeSlotCount,
                expiredAt,
                margin
            );
            
            System.out.println("Constructor data: 0x" + org.web3j.utils.Numeric.toHexString(constructorData));
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}