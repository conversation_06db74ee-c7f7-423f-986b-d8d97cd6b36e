package com.zeek.core.server.business.prediction.action;

import com.alibaba.fastjson.JSON;
import com.kikitrade.framework.common.util.ChainIdUtil;
import com.zeek.core.api.respond.ZeekCoreRespondCode;
import com.zeek.core.common.constants.ZeekConstants;
import com.zeek.core.common.exception.BaseException;
import com.zeek.core.common.statemachine.BaseEventAction;
import com.zeek.core.dal.ots.model.PredictionMarketsDO;
import com.zeek.core.server.business.prediction.model.MarketActionContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigInteger;

/**
 * @author: joseph.xiang
 * @description: 创建市场动作
 * @create: 2025-05-16
 **/
@Component
@Slf4j
public class CreateMarketEventAction extends BaseMarketEventAction implements BaseEventAction {

    @Override
    public boolean execute(Object context) throws BaseException {
        MarketActionContext marketContext = (MarketActionContext) context;

        // 生成条件ID
        String conditionId = marketContext.getId();

        // 创建市场记录
        PredictionMarketsDO marketDO = new PredictionMarketsDO();
        marketDO.setConditionId(conditionId);
        marketDO.setCustomerId(marketContext.getCustomerId());
        marketDO.setQuestionId(marketContext.getQuestionId());
        marketDO.setChainId(ChainIdUtil.getChainId());
        marketDO.setTitle(marketContext.getTitle());
        marketDO.setContent(marketContext.getContent());
        marketDO.setSlot(marketContext.getSlot());
        marketDO.setMedias(marketContext.getMedias());
        marketDO.setToken(marketContext.getToken());
        marketDO.setValue(marketContext.getValue());
        marketDO.setEndTime(marketContext.getEndTime());
        marketDO.setVolume(BigInteger.ZERO.toString());
        marketDO.setVolumeUsd(0.0);
        marketDO.setVolumeSort(0.0);
        marketDO.setFunding(BigInteger.ZERO.toString());
        marketDO.setStatus(ZeekConstants.MarketStatus.Pending.name());
        marketDO.setOracleType(marketContext.getOracleType().name());
        marketDO.setVersion(0L);
        marketDO.setCreated(System.currentTimeMillis());
        marketDO.setModified(System.currentTimeMillis());

        // 保存市场记录
        boolean result = predictionMarketsBuilder.putRow(marketDO);
        if (!result) {
            log.error("Failed to create market: {}", JSON.toJSONString(marketDO));
            throw new BaseException(ZeekCoreRespondCode.MARKET_CREATE_FAILED);
        }

        log.info("Market created successfully: {}", conditionId);
        return true;
    }
}
