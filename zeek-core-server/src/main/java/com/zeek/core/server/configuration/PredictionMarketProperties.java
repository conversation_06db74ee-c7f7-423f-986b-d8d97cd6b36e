package com.zeek.core.server.configuration;

/**
 * @author: liz<PERSON><PERSON>
 * @date: 2025/5/15 11:55
 */

import com.google.common.collect.Lists;
import com.zeek.core.common.constants.ZeekConstants;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: lizhifeng
 * @date: 2025/5/15 11:55
 */
@Data
@RefreshScope
@ConfigurationProperties(prefix = "prediction")
@Configuration
public class PredictionMarketProperties {

    private List<TokenConfig> tokens;
    private DaysConfig days;
    private FeeConfig fee;
    private VoteConfig vote;
    private OracleConfig oracle;

    private String lmsrEngineHost;
    private String marketContractName;
    private String marketContractVersion;
    private String implementationMaster = "0x0ea2845e2610b60D50C4605E8BfF101573b14D3d";

    private List<String> operateWhitelist;

    @Data
    public static class TokenConfig {
        private String name;
        private String symbol;
        private String address;
        private String collateralMin;
        private String collateralMax;
        private String slotUnit;
    }

    @Data
    public static class DaysConfig {
        private Integer min;
        private Integer max;
    }

    @Data
    public static class FeeConfig {
        private Integer total;
        private Integer platform;
        private Integer poster;
        private Integer answer;
    }

    @Data
    public static class VoteConfig {
        private Integer min;
        private Integer max;
        private List<Integer> options;
    }

    @Data
    public static class OracleConfig {
        private ZeekConstants.PredictionOracleType type;
        private List<ZeekConstants.PredictionOracleType> options;
    }

    public List<String> getOperateWhitelist() {
        if (CollectionUtils.isEmpty(operateWhitelist)) {
            return Lists.newArrayList();
        }

        return operateWhitelist.stream().map(String::toLowerCase).collect(Collectors.toList());
    }
}
