package com.zeek.core.server.business.prediction.service.impl;

import co.evg.scaffold.innerEvent.client.InnerEventClient;
import co.evg.scaffold.innerEvent.common.model.InnerEventDTO;
import com.alibaba.fastjson.JSON;
import com.kikitrade.framework.common.util.ChainIdUtil;
import com.zeek.core.api.PredictionOutcomesRequest;
import com.zeek.core.api.respond.ZeekCoreRespondCode;
import com.zeek.core.common.constants.CoreInnerEventConstants;
import com.zeek.core.common.constants.ZeekConstants;
import com.zeek.core.common.exception.BaseException;
import com.zeek.core.contract.common.EventTypeConstants;
import com.zeek.core.contract.model.event.prediction.MarketAMMFundingAddedEventLog;
import com.zeek.core.contract.service.ILMSRMarketService;
import com.zeek.core.dal.ots.builder.PredictionMarketsBuilder;
import com.zeek.core.dal.ots.builder.PredictionOutcomesBuilder;
import com.zeek.core.dal.ots.builder.ProfileBasicBuilder;
import com.zeek.core.dal.ots.model.PredictionMarketsDO;
import com.zeek.core.dal.ots.model.PredictionOutcomesDO;
import com.zeek.core.dal.ots.model.ProfileBasicDO;
import com.zeek.core.server.business.prediction.service.PredictionOutcomesService;
import com.zeek.core.server.business.prediction.service.PredictionPositionsService;
import com.zeek.core.server.business.prediction.service.PredictionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.web3j.protocol.Web3j;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;

/**
 * @author: lizhifeng
 * @date: 2025/5/15 19:04
 */
@Service
@Slf4j
public class PredictionOutcomesServiceImpl implements PredictionOutcomesService {


    @Resource
    @Qualifier("web3js")
    Map<String, Web3j> web3js;
    @Resource
    private ILMSRMarketService lmsrMarketService;
    @Resource
    private PredictionService predictionService;
    @Resource
    private PredictionPositionsService positionsService;
    @Resource
    InnerEventClient innerEventClient;
    @Resource
    PredictionOutcomesBuilder outcomesBuilder;
    @Resource
    PredictionMarketsBuilder marketsBuilder;
    @Resource
    ProfileBasicBuilder profileBasicBuilder;

    @Override
    public boolean createOutcome(PredictionOutcomesRequest request) throws BaseException {
        if (StringUtils.isAnyBlank(request.getCustomerId(), request.getMarketId(), request.getToken(), request.getValue(), request.getContent())) {
            throw new BaseException(ZeekCoreRespondCode.PARAM_ILLEGAL);
        }

        PredictionMarketsDO marketsDO = marketsBuilder.getRowByConditionId(request.getMarketId());
        if (marketsDO == null) {
            throw new BaseException(ZeekCoreRespondCode.MARKET_NOT_EXIST);
        }
        if (marketsDO.getEndTime() < System.currentTimeMillis() ||
                StringUtils.equalsAnyIgnoreCase(marketsDO.getStatus(), ZeekConstants.MarketStatus.Ended.name(), ZeekConstants.MarketStatus.Closed.name())) {
            throw new BaseException(ZeekCoreRespondCode.MARKET_CLOSED);
        }
        if (StringUtils.equalsIgnoreCase(marketsDO.getCustomerId(), request.getCustomerId())) {
            throw new BaseException(ZeekCoreRespondCode.OUTCOME_NOT_ALLOWED);
        }
        List<PredictionOutcomesDO> outcomes = outcomesBuilder.getRows(request.getMarketId(), ZeekConstants.OutcomeStatus.NORMAL.getCode());
        if (marketsDO.getSlot() <= outcomes.size()) {
            throw new BaseException(ZeekCoreRespondCode.MARKET_SLOT_FULL);
        }

        // 用户是否回答过
        PredictionOutcomesDO outcomesDO = outcomesBuilder.getRow(request.getMarketId(), request.getCustomerId());
        if (outcomesDO != null && outcomesDO.getStatus() != ZeekConstants.OutcomeStatus.PENDING.getCode()) {
            throw new BaseException(ZeekCoreRespondCode.OUTCOME_ALREADY_EXIST);
        }

        // 创建 outcome
        if (outcomesDO == null) {
            PredictionOutcomesDO predictionOutcomesDO = new PredictionOutcomesDO();
            predictionOutcomesDO.setMarketId(request.getMarketId());
            predictionOutcomesDO.setCustomerId(request.getCustomerId());
            predictionOutcomesDO.setChainId(String.valueOf(ChainIdUtil.getChainId()));
            predictionOutcomesDO.setContent(request.getContent());
            predictionOutcomesDO.setToken(request.getToken());
            predictionOutcomesDO.setValue(request.getValue());
            predictionOutcomesDO.setStatus(ZeekConstants.OutcomeStatus.PENDING.getCode());
            predictionOutcomesDO.setVolume(BigInteger.ZERO.toString());
            predictionOutcomesDO.setVolumeUsd(0.0);
            predictionOutcomesDO.setTotalPosition(BigInteger.ZERO.toString());
            predictionOutcomesDO.setVersion(0L);
            predictionOutcomesDO.setCreated(System.currentTimeMillis());
            predictionOutcomesDO.setModified(System.currentTimeMillis());
            return outcomesBuilder.putPow(predictionOutcomesDO);
        } else {
            outcomesDO.setContent(request.getContent());
            outcomesDO.setToken(request.getToken());
            outcomesDO.setValue(request.getValue());
            outcomesDO.setCreated(System.currentTimeMillis());
            outcomesDO.setModified(System.currentTimeMillis());
            return outcomesBuilder.putPow(outcomesDO);
        }
    }

    @Override
    public boolean processOnChain(MarketAMMFundingAddedEventLog eventLog) throws BaseException {
        log.info("market outcome processOnChain eventLog:{}", JSON.toJSONString(eventLog));
        PredictionMarketsDO marketsDO = marketsBuilder.getByMarketAddress(eventLog.getMarketAddress());
        if (marketsDO == null) {
            log.warn("market not exist, marketAddress: {}", eventLog.getMarketAddress());
            throw new BaseException(ZeekCoreRespondCode.MARKET_NOT_EXIST);
        }

        ProfileBasicDO proposalProfile = profileBasicBuilder.getByAddress(eventLog.getProposal());
        if (proposalProfile == null) {
            log.warn("proposal profile not exist, proposalAddress: {}", eventLog.getProposal());
            throw new BaseException(ZeekCoreRespondCode.PROFILE_NOT_EXIST);
        }

        PredictionOutcomesDO outcomesDO = outcomesBuilder.getRow(marketsDO.getConditionId(), proposalProfile.getCustomerId());
        if (outcomesDO == null) {
            log.warn("outcome not exist, marketId: {}, customerId: {}", marketsDO.getConditionId(), proposalProfile.getCustomerId());
            throw new BaseException(ZeekCoreRespondCode.OUTCOME_NOT_EXIST);
        }

        // 调用合约获取 positionId
        BigInteger positionId = lmsrMarketService.getPositionId(web3js.get(String.valueOf(ChainIdUtil.getChainId())), eventLog.getMarketAddress(), eventLog.getOutcomeSlot().intValue());

        // 更新 outcome slot、 status、token、value modified
        outcomesDO.setSlot(eventLog.getOutcomeSlot().intValue());
        outcomesDO.setStatus(ZeekConstants.OutcomeStatus.NORMAL.getCode());
        outcomesDO.setToken(marketsDO.getToken());
        outcomesDO.setValue(eventLog.getFundingChange().toString());
        outcomesDO.setModified(System.currentTimeMillis());
        outcomesDO.setPositionId(positionId != null ? positionId.toString() : null);
        boolean success = outcomesBuilder.setStatusNormal(outcomesDO);

        // 更新 market 池子大小
        boolean b = predictionService.updateFunding(marketsDO.getConditionId(), eventLog.getFundingPool(), eventLog.getTimestamp());

        innerEventClient.pushEvent(new InnerEventDTO()
                .setGlobalUid(StringUtils.joinWith("_", EventTypeConstants.MARKET_AMM_FUNDING_ADDED_EVENT, eventLog.getLog().getTransactionHash()))
                .setName(CoreInnerEventConstants.MARKET_FUNDING_ADDED)
                .addBody(CoreInnerEventConstants.MARKET, marketsDO)
                .addBody(CoreInnerEventConstants.MARKET_OUTCOME, outcomesDO)
                .addBody(CoreInnerEventConstants.EVENT, eventLog));

        innerEventClient.pushEvent(new InnerEventDTO()
                .setGlobalUid(StringUtils.joinWith("_", EventTypeConstants.MARKET_AMM_FUNDING_ADDED_EVENT, eventLog.getLog().getTransactionHash()))
                .setName(CoreInnerEventConstants.WALLET_TRANSACTION)
                .addBody(CoreInnerEventConstants.WALLET_TRANSACTION_TYPE, CoreInnerEventConstants.WALLET_TRANSACTION_PREDICTION_OUTCOME)
                .addBody(CoreInnerEventConstants.AA_ADDRESS, proposalProfile.getAddress())
                .addBody(CoreInnerEventConstants.MARKET, marketsDO)
                .addBody(CoreInnerEventConstants.EVENT, eventLog));
        return success && b;
    }

    @Override
    public boolean updatePosition(String conditionId, int slot, BigInteger totalPosition, BigInteger timestamp) {
        PredictionOutcomesDO outcomesDO = outcomesBuilder.getRowBySlot(conditionId, slot);
        if (outcomesDO == null) {
            log.warn("outcome not exist, marketId: {}, slot: {}", conditionId, slot);
            return false;
        }

        if (outcomesDO.getEventTime() != null && outcomesDO.getEventTime() >= timestamp.longValue()) {
            log.warn("outcome position already updated, marketId: {}, slot: {}, eventTime: {}, timestamp: {}", conditionId, slot, outcomesDO.getEventTime(), timestamp);
            return true;
        }

        log.info("market update position, marketId: {}, slot: {}, oldTotalPosition: {}, eventTime: {}, totalPosition: {}, timestamp: {}",
                conditionId, slot, outcomesDO.getTotalPosition(), outcomesDO.getEventTime(), totalPosition, timestamp);
        return outcomesBuilder.updateTotalPosition(outcomesDO, totalPosition.toString(), timestamp.longValue());
    }

    @Override
    public boolean closeOutcomes(String conditionId, List<Integer> payouts, Long eventTime) {
        log.info("prediction update outcome status to perfect, marketId: {}, payouts: {}", conditionId, payouts);
        PredictionMarketsDO marketsDO = marketsBuilder.getRowByConditionId(conditionId);
        if (CollectionUtils.isEmpty(payouts)) {
            log.warn("payouts is empty, marketId: {}", conditionId);
            return true;
        }

        for (int i = 0; i < payouts.size(); i++) {
            PredictionOutcomesDO outcomeDO = outcomesBuilder.getRowBySlot(conditionId, payouts.get(i));

            // 虚拟份额累加
            BigInteger virtualPosition = marketsDO.getSlotUnit().multiply(BigInteger.valueOf(payouts.size()));
            BigInteger totalPosition = virtualPosition.add(new BigInteger(outcomeDO.getTotalPosition()));
            boolean tb = outcomesBuilder.updateTotalPosition(outcomeDO, totalPosition.toString(), eventTime);
            if (!tb) {
                log.error("Failed to update outcome total position, marketId: {}, slot: {}", conditionId, outcomeDO.getSlot());
                return false;
            }

            // 选项提供者持仓累加
            boolean pb = positionsService.updatePositions(outcomeDO.getCustomerId(), conditionId, payouts.get(i), virtualPosition, eventTime);
            if (!pb) {
                log.error("Failed to update outcome provider virtual position, customerId: {}, marketId: {}, slot: {}, virtualPosition: {}, eventTime: {}",
                        outcomeDO.getCustomerId(), conditionId, outcomeDO.getSlot(), virtualPosition, eventTime);
                return false;
            }

            if (payouts.get(i) == 0) {
                continue;
            }

            // 修改状态
            boolean ob = outcomesBuilder.setStatusPerfect(outcomeDO);
            if (!ob) {
                log.error("Failed to update outcome status to perfect, marketId: {}, slot: {}", conditionId, outcomeDO.getSlot());
                return false;
            }
        }
        log.info("prediction update outcome status to perfect successfully, marketId: {}, payouts: {}", conditionId, payouts);
        return true;
    }

}
