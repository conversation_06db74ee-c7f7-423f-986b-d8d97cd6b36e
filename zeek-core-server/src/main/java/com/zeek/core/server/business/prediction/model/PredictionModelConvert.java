package com.zeek.core.server.business.prediction.model;

import com.zeek.core.api.*;
import com.zeek.core.common.constants.ZeekConstants;
import com.zeek.core.dal.ots.model.PredictionMarketsDO;
import com.zeek.core.dal.ots.model.PredictionOutcomesDO;
import com.zeek.core.dal.ots.model.PredictionPositionsDO;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigInteger;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: xiang.bai
 * @description:
 * @create: 2024-09-12
 **/
public class PredictionModelConvert {


    public static PredictionMarketDTO toMarketDTO(PredictionMarketsDO predictionMarketsDO) {
        if (predictionMarketsDO == null) {
            return null;
        }

        PredictionMarketDTO.Builder builder = PredictionMarketDTO.newBuilder();

        // Set basic fields
        builder.setConditionId(Optional.ofNullable(predictionMarketsDO.getConditionId()).orElse(""))
               .setCustomerId(Optional.ofNullable(predictionMarketsDO.getCustomerId()).orElse(""))
               .setChainId(predictionMarketsDO.getChainId())
               .setQuestionId(Optional.ofNullable(predictionMarketsDO.getQuestionId()).orElse(""))
               .setMarketAddress(Optional.ofNullable(predictionMarketsDO.getMarketAddress()).orElse(""))
               .setTitle(Optional.ofNullable(predictionMarketsDO.getTitle()).orElse(""))
               .setContent(Optional.ofNullable(predictionMarketsDO.getContent()).orElse(""));

        // Set media list
        if (CollectionUtils.isNotEmpty(predictionMarketsDO.getMedias())) {
            builder.addAllMedias(predictionMarketsDO.getMedias());
        }

        // Set token and value
        builder.setToken(Optional.ofNullable(predictionMarketsDO.getToken()).orElse(""))
               .setValue(Optional.ofNullable(predictionMarketsDO.getValue()).map(String::valueOf).orElse(""))
               .setSlot(Optional.ofNullable(predictionMarketsDO.getSlot()).map(String::valueOf).orElse(""));

        // Set numeric values
        builder.setVolume(Optional.ofNullable(predictionMarketsDO.getVolume()).orElse("0"))
               .setVolumeUsd(Optional.ofNullable(predictionMarketsDO.getVolumeUsd()).orElse(0.0))
               .setVote(Optional.ofNullable(predictionMarketsDO.getVote()).orElse(0L));

        // Set timestamps
        builder.setEndTime(Optional.ofNullable(predictionMarketsDO.getEndTime()).orElse(0L))
               .setCreated(Optional.ofNullable(predictionMarketsDO.getCreated()).orElse(0L))
               .setModified(Optional.ofNullable(predictionMarketsDO.getModified()).orElse(0L));

        // Outcomes would typically be fetched separately and added here if needed
        // For now, we're returning an empty list

        return builder.build();
    }

    public static PredictionOutComesDTO toOutComesDTO(PredictionOutcomesDO predictionOutcomesDO) {
        PredictionOutComesDTO.Builder builder = PredictionOutComesDTO.newBuilder();
        builder.setCustomerId(Optional.ofNullable(predictionOutcomesDO.getCustomerId()).orElse(""));
        builder.setContent(Optional.ofNullable(predictionOutcomesDO.getContent()).orElse(""));
        builder.setStatus(ZeekConstants.OutcomeStatus.get(predictionOutcomesDO.getStatus()).name());
        builder.setSlot(Optional.ofNullable(predictionOutcomesDO.getSlot()).orElse(0));
        builder.setNickName(Optional.ofNullable(predictionOutcomesDO.getNickName()).orElse(""));
        builder.setCreated(Optional.ofNullable(predictionOutcomesDO.getCreated()).orElse(0L));
        builder.setHandle(Optional.ofNullable(predictionOutcomesDO.getHandle()).orElse(""));
        builder.setVolume(Optional.ofNullable(predictionOutcomesDO.getVolume()).orElse("0"));
        builder.setMyVote(Optional.ofNullable(predictionOutcomesDO.getMyVote()).orElse("0"));
        builder.setVote(Optional.ofNullable(predictionOutcomesDO.getVote()).orElse("0"));
        builder.setChance(Optional.ofNullable(predictionOutcomesDO.getChance()).orElse(0.0D).floatValue());
        return builder.build();
    }
    public static PredictionMarketDTO toMarketDTO(PredictionMarketsDO predictionMarketsDO, List<PredictionOutcomesDO> outcomes, boolean hasParticipant, ProfileOpenSocialDTO profileOpenSocialDTO) {
        return toMarketDTO(predictionMarketsDO, outcomes, hasParticipant, profileOpenSocialDTO, 0L);
    }
    public static PredictionMarketDTO toMarketDTO(PredictionMarketsDO predictionMarketsDO, List<PredictionOutcomesDO> outcomes, boolean hasParticipant, ProfileOpenSocialDTO profileOpenSocialDTO, Long winPoint) {
        if (predictionMarketsDO == null) {
            return null;
        }

        PredictionMarketDTO.Builder builder = PredictionMarketDTO.newBuilder();
        // Set basic fields
        builder.setConditionId(Optional.ofNullable(predictionMarketsDO.getConditionId()).orElse(""))
                .setCustomerId(Optional.ofNullable(predictionMarketsDO.getCustomerId()).orElse(""))
                .setChainId(predictionMarketsDO.getChainId())
                .setQuestionId(Optional.ofNullable(predictionMarketsDO.getQuestionId()).orElse(""))
                .setMarketAddress(Optional.ofNullable(predictionMarketsDO.getMarketAddress()).orElse(""))
                .setTitle(Optional.ofNullable(predictionMarketsDO.getTitle()).orElse(""))
                .setSlot(Optional.ofNullable(predictionMarketsDO.getSlot()).map(String::valueOf).orElse(""))
                .setStatus(Optional.ofNullable(predictionMarketsDO.getStatus()).orElse(""))
                .setHasParticipant(hasParticipant)
                .setWinPoint(String.valueOf(winPoint))
                .setContent(Optional.ofNullable(predictionMarketsDO.getContent()).orElse(""));

        if (profileOpenSocialDTO != null) {
            builder.setPosterHandle(profileOpenSocialDTO.getHandle())
                .setNickName(profileOpenSocialDTO.getNickName())
                .setAvatar(profileOpenSocialDTO.getAvatar());
        }
        // Set media list
        if (CollectionUtils.isNotEmpty(predictionMarketsDO.getMedias())) {
            builder.addAllMedias(predictionMarketsDO.getMedias());
        }

        // Set token and value
        builder.setToken(Optional.ofNullable(predictionMarketsDO.getToken()).orElse(""))
                .setValue(Optional.ofNullable(predictionMarketsDO.getValue()).map(String::valueOf).orElse(""))
                .setSlot(Optional.ofNullable(predictionMarketsDO.getSlot()).map(String::valueOf).orElse(""));

        // Set numeric values
        builder.setVolume(Optional.ofNullable(predictionMarketsDO.getVolume()).orElse("0"))
                .setVolumeUsd(Optional.ofNullable(predictionMarketsDO.getVolumeUsd()).orElse(0.0))
                .setVote(Optional.ofNullable(predictionMarketsDO.getVote()).orElse(0L));

        // Set timestamps
        builder.setEndTime(Optional.ofNullable(predictionMarketsDO.getEndTime()).orElse(0L))
                .setCreated(Optional.ofNullable(predictionMarketsDO.getCreated()).orElse(0L))
                .setModified(Optional.ofNullable(predictionMarketsDO.getModified()).orElse(0L));

        builder.addAllOutcomes(outcomes.stream().map(PredictionModelConvert::toOutComesDTO).collect(Collectors.toList()));
        // Outcomes would typically be fetched separately and added here if needed
        // For now, we're returning an empty list

        return builder.build();
    }

    public static MarketPositionsDTO of(int slot, PredictionPositionsDO positionsDO, BigInteger slotUnit) {
        String position = BigInteger.ZERO.toString();
        if (positionsDO != null) {
            position = new BigInteger(positionsDO.getValue()).divide(slotUnit).toString();
        }
        return MarketPositionsDTO.newBuilder()
                .setSlot(slot)
                .setPosition(position)
                .build();
    }

}
