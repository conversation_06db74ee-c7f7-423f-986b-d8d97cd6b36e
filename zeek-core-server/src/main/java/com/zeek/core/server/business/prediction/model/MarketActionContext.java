package com.zeek.core.server.business.prediction.model;

import co.evg.scaffold.endpoint.server.contract.model.BaseEventLog;
import com.zeek.core.common.constants.ZeekConstants;
import com.zeek.core.common.statemachine.BaseContext;
import com.zeek.core.contract.model.event.prediction.MarketAMMClosedEventLog;
import com.zeek.core.contract.model.event.prediction.MarketAMMCreatedEventLog;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;

/**
 * @author: joseph.xiang
 * @description: 市场状态机上下文
 * @create: 2025-05-16
 **/
@Data
@SuperBuilder
public class MarketActionContext extends BaseContext {
    String marketId;
    String conditionId;
    String customerId;
    Long chainId;
    String questionId;
    String marketAddress;
    String title;
    String content;
    List<String> medias;
    String token;
    String value;
    Long slot;
    Long volume;
    Long volumeUsd;
    Long vote;
    Long endTime;
    Long created;
    Long modified;
    BigInteger collateralUnit;
    ZeekConstants.PredictionOracleType oracleType;

    // 其他可能需要的字段
    String transactionHash;
    List<Integer> payouts;
    Map<String, String> extMap;

    String fee;
    String creatorFee;
    String proposalFee;
    String adminAddress;
    String financeAddress;
    String oracleAddress;

    MarketAMMCreatedEventLog eventLog;
    MarketAMMClosedEventLog closedEventLog;
}
