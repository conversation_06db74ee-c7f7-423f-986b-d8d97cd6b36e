package com.zeek.core.server.business.prediction.oracle.impl;

import com.zeek.core.common.exception.BaseException;
import com.zeek.core.server.business.prediction.oracle.PredictionOracleService;
import com.zeek.core.server.business.prediction.service.PredictionVoteService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.List;

/**
 * @author: liz<PERSON><PERSON>
 * @date: 2025/6/9 20:00
 */
@Slf4j
@Component
public class PredictionVoteOracleService implements PredictionOracleService {

    @Resource
    PredictionVoteService predictionVoteService;

    @Override
    public List<BigInteger> getMarketResult(String marketId, int winSlot) throws BaseException {
        return predictionVoteService.getMarketVoteResult(marketId);
    }
}
