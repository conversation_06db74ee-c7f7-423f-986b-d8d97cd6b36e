package com.zeek.core.server.business.prediction.service.impl;

import com.alibaba.fastjson.JSON;
import com.kikitrade.framework.common.util.ChainIdUtil;
import com.zeek.core.api.MarketPositionsDTO;
import com.zeek.core.api.PositionsRequest;
import com.zeek.core.api.respond.ZeekCoreRespondCode;
import com.zeek.core.common.exception.BaseException;
import com.zeek.core.dal.ots.builder.PredictionMarketsBuilder;
import com.zeek.core.dal.ots.builder.PredictionPositionsBuilder;
import com.zeek.core.dal.ots.model.PredictionMarketsDO;
import com.zeek.core.dal.ots.model.PredictionPositionsDO;
import com.zeek.core.server.business.prediction.model.PredictionModelConvert;
import com.zeek.core.server.business.prediction.service.PredictionPositionsService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: lizhifeng
 * @date: 2025/5/19 19:04
 */
@Service
@Slf4j
public class PredictionPositionsServiceImpl implements PredictionPositionsService {

    @Resource
    private PredictionMarketsBuilder marketsBuilder;
    @Resource
    private PredictionPositionsBuilder positionsBuilder;

    @Override
    public PredictionPositionsDO getPositions(String customerId, String marketId, Integer slot) {
        if (StringUtils.isEmpty(customerId) || StringUtils.isEmpty(marketId) || slot == null){
            return null;
        }
        return positionsBuilder.getPow(customerId, marketId, slot);
    }

    @Override
    public boolean updatePositions(String customerId, String marketId, Integer slot, BigInteger amount, Long eventTime) {
        log.info("prediction update positions, customerId: {}, marketId: {}, slot: {}, amount: {}, eventTime: {}",  customerId, marketId, slot, amount, eventTime);
        // 修改用户份额
        PredictionPositionsDO positionsDO = positionsBuilder.getPow(customerId, marketId, slot);
        if (positionsDO == null) {
            positionsDO = buildPositionDO(customerId, marketId, slot, amount, eventTime);
            boolean pb = positionsBuilder.putPow(positionsDO);
            if (!pb) {
                log.warn("prediction positions put fail, positionsDO: {}", JSON.toJSONString(positionsDO));
                return false;
            }
            return true;
        } else {
            long timestamp = eventTime.longValue();
            if (positionsDO.getEventTime() != null && positionsDO.getEventTime() >= timestamp) {
                log.warn("prediction positions already updated, customerId: {}, marketId: {}, slot: {}, oldPosition: {}, eventTime: {}, newPosition: {}, timestamp: {}",
                        customerId, marketId, positionsDO.getSlot(), positionsDO.getValue(), positionsDO.getEventTime(), amount, timestamp);
                return true;
            }

            log.info("prediction positions update, customerId: {}, marketId: {}, slot: {}, oldPosition: {}, eventTime: {}, newPosition: {}, timestamp: {}",
                    customerId, marketId, positionsDO.getSlot(), positionsDO.getValue(), positionsDO.getEventTime(), amount, timestamp);
            positionsDO.setValue(new BigInteger(positionsDO.getValue()).add(amount).toString());
            boolean pb = positionsBuilder.updateValue(positionsDO, timestamp);
            if (!pb) {
                log.warn("prediction positions update fail, positionsDO: {}", JSON.toJSONString(positionsDO));
                return false;
            }
            return true;
        }
    }

    @Override
    public List<MarketPositionsDTO> positions(PositionsRequest request) throws BaseException {
        log.info("prediction positions request:{}", request);
        if (StringUtils.isEmpty(request.getMarketId()) || StringUtils.isEmpty(request.getCustomerId())) {
            throw new BaseException(ZeekCoreRespondCode.PARAM_ILLEGAL);
        }

        PredictionMarketsDO marketsDO = marketsBuilder.getRowByConditionId(request.getMarketId());
        if (marketsDO == null) {
            throw new BaseException(ZeekCoreRespondCode.MARKET_NOT_EXIST);
        }

        List<MarketPositionsDTO> result = new ArrayList<>();
        if (request.getSlot() == -1) {
            List<PredictionPositionsDO> positionsDOS = positionsBuilder.queryByMarketId(request.getCustomerId(), request.getMarketId());
            for (PredictionPositionsDO positionsDO : positionsDOS) {
                result.add(PredictionModelConvert.of(positionsDO.getSlot(), positionsDO, marketsDO.getSlotUnit()));
            }
        } else {
            PredictionPositionsDO positionsDO = positionsBuilder.getPow(request.getCustomerId(), request.getMarketId(), request.getSlot());
            result.add(PredictionModelConvert.of(request.getSlot(), positionsDO, marketsDO.getSlotUnit()));
        }
        log.info("prediction positions end, result: {}", result);
        return result;
    }

    private static PredictionPositionsDO buildPositionDO(String customerId, String marketId, Integer slot, BigInteger amount, Long eventTime) {
        PredictionPositionsDO positionsDO;
        positionsDO = new PredictionPositionsDO();
        positionsDO.setCustomerId(customerId);
        positionsDO.setMarketId(marketId);
        positionsDO.setSlot(slot);
        positionsDO.setChainId(String.valueOf(ChainIdUtil.getChainId()));
        positionsDO.setValue(amount.toString());
        positionsDO.setVersion(0L);
        positionsDO.setEventTime(eventTime);
        positionsDO.setCreated(System.currentTimeMillis());
        positionsDO.setModified(System.currentTimeMillis());
        return positionsDO;
    }
}
