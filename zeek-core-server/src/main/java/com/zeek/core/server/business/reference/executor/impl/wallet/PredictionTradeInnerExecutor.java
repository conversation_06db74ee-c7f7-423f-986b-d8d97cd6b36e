package com.zeek.core.server.business.reference.executor.impl.wallet;

import co.evg.scaffold.innerEvent.common.annotation.ExecutorTopics;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kikitrade.framework.common.util.ChainIdUtil;
import com.zeek.core.common.constants.CoreInnerEventConstants;
import com.zeek.core.common.constants.ZeekConstants;
import com.zeek.core.common.constants.ZeekConstants.WalletTransactionCategory;
import com.zeek.core.common.constants.ZeekConstants.WalletTransactionType;
import com.zeek.core.contract.model.event.prediction.MarketAMMTokenTradeEventLog;
import com.zeek.core.dal.ots.model.PredictionMarketsDO;
import com.zeek.core.dal.ots.model.WalletTransactionDO;
import com.zeek.core.server.business.reference.service.IPriceReference;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.List;

/**
 * @author: xiang.bai
 * @description:
 * @create: 2024-08-29
 **/
@Component
@Slf4j
@ExecutorTopics(value = {CoreInnerEventConstants.WALLET_TRANSACTION})
public class PredictionTradeInnerExecutor extends AbstractWalletTransactionInnerExecutor<MarketAMMTokenTradeEventLog> {

    @Resource
    IPriceReference priceReference;

    @Override
    public String getExecutorEventType() {
        return CoreInnerEventConstants.WALLET_TRANSACTION_PREDICTION_TRADE;
    }

    @Override
    public List<WalletTransactionDO> assembleWalletTransactionDO(MarketAMMTokenTradeEventLog eventLog, JSONObject input) {
        log.info("PredictionTradeInnerExecutor assembleWalletTransactionDO, eventLog: {}, input: {}", JSON.toJSONString(eventLog), JSON.toJSONString(input));
        String transactionHash = eventLog.getLog().getTransactionHash();
        String address = input.getString(CoreInnerEventConstants.AA_ADDRESS);
        PredictionMarketsDO marketsDO = input.getObject(CoreInnerEventConstants.MARKET, PredictionMarketsDO.class);

        WalletTransactionDO walletTransactionDO = new WalletTransactionDO();
        walletTransactionDO.setType(WalletTransactionType.PREDICTION_TRADE);
        walletTransactionDO.setCategory(WalletTransactionCategory.PREDICTION_TRADE);
        walletTransactionDO.setAddress(address);
        walletTransactionDO.setChainId(ChainIdUtil.getChainId());
        walletTransactionDO.setTimestamp(System.currentTimeMillis());
        walletTransactionDO.setUniqueId(WalletTransactionType.PREDICTION_TRADE, transactionHash);
        walletTransactionDO.setTransactionHash(transactionHash);
        walletTransactionDO.setTitle("Trade Answer");
        walletTransactionDO.setToken(marketsDO.getToken());
        boolean isSend = eventLog.getAmount().compareTo(BigInteger.ZERO) > 0;
        walletTransactionDO.setDirection(isSend ? ZeekConstants.WalletTransactionDirection.SEND.getCode() : ZeekConstants.WalletTransactionDirection.RECEIVE.getCode());
        // 实际花费或者是实际收到的钱
        walletTransactionDO.setValue(eventLog.getNetCost().abs().toString());
        walletTransactionDO.setSymbol(getTokenSymbol(marketsDO.getToken()));
        String usdValue = priceReference.exchangeUSD(getTokenSymbol(marketsDO.getToken()), walletTransactionDO.getValue());
        if (StringUtils.isNotBlank(usdValue)) {
            walletTransactionDO.setValueUsd(usdValue);
        }
        walletTransactionDO.setCreated(System.currentTimeMillis());
        walletTransactionDO.setModified(System.currentTimeMillis());

        return List.of(walletTransactionDO);
    }

    @Override
    protected Class<MarketAMMTokenTradeEventLog> getEventLogClass() {
        return MarketAMMTokenTradeEventLog.class;
    }

}
