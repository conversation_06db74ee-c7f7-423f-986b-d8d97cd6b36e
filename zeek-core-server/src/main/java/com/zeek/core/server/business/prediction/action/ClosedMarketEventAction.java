package com.zeek.core.server.business.prediction.action;

import co.evg.scaffold.innerEvent.common.model.InnerEventDTO;
import com.alibaba.fastjson.JSON;
import com.zeek.core.api.respond.ZeekCoreRespondCode;
import com.zeek.core.common.constants.CoreInnerEventConstants;
import com.zeek.core.common.constants.ZeekConstants;
import com.zeek.core.common.exception.BaseException;
import com.zeek.core.common.statemachine.BaseEventAction;
import com.zeek.core.contract.common.EventTypeConstants;
import com.zeek.core.dal.ots.model.PredictionMarketsDO;
import com.zeek.core.dal.ots.model.PredictionOutcomesDO;
import com.zeek.core.server.business.prediction.model.MarketActionContext;
import com.zeek.core.server.business.prediction.service.PredictionOutcomesService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: joseph.xiang
 * @description: 关闭市场动作
 * @create: 2025-05-16
 **/
@Component
@Slf4j
public class ClosedMarketEventAction extends BaseMarketEventAction implements BaseEventAction {

    @Resource
    @Lazy
    PredictionOutcomesService predictionOutcomesService;

    @Override
    public boolean execute(Object context) throws BaseException {
        MarketActionContext marketContext = (MarketActionContext) context;

        // 获取市场记录
        PredictionMarketsDO marketDO = getMarketByConditionId(marketContext.getConditionId());

        if (marketDO == null) {
            log.error("Market not found: {}", marketContext.getConditionId());
            throw new BaseException(ZeekCoreRespondCode.MARKET_NOT_FOUND);
        }

        // 检查状态
        if (!ZeekConstants.MarketStatus.Ended.name().equals(marketDO.getStatus())) {
            log.warn("Market status is not Ended: {}, current status: {}",
                    marketDO.getConditionId(), marketDO.getStatus());
            return false;
        }
        if (StringUtils.equalsIgnoreCase(ZeekConstants.MarketStatus.Closed.name(), marketDO.getStatus())) {
            log.warn("Market status is already Closed: {}", marketContext.getConditionId());
            return true;
        }

        // 更新市场状态
        marketDO.setStatus(ZeekConstants.MarketStatus.Closed.name());
        marketDO.setModified(System.currentTimeMillis());

        boolean result = predictionMarketsBuilder.updateRow(marketDO);
        if (!result) {
            log.error("Failed to close market: {}", JSON.toJSONString(marketDO));
            throw new BaseException(ZeekCoreRespondCode.MARKET_UPDATE_FAILED);
        }

        // 关闭市场所有预测
        long eventTime = marketContext.getClosedEventLog().getTimestamp();
        boolean cb = predictionOutcomesService.closeOutcomes(marketDO.getConditionId(), marketContext.getPayouts(), eventTime);
        if (!cb) {
            log.error("Failed to closeOutcomes market outcomes: {}", JSON.toJSONString(marketDO));
            throw new BaseException(ZeekCoreRespondCode.MARKET_OUTCOME_UPDATE_FAILED);
        }

        // 获取市场的outcomes来确定slot数量
        List<PredictionOutcomesDO> outcomesDOS = predictionOutcomesBuilder.getEffectiveRows(marketDO.getConditionId());
        int outcomeCount = outcomesDOS.size();

        log.info("Market: {}, outcome count: {}", marketDO.getConditionId(), outcomeCount);

        // 发送市场关闭事件
        innerEventClient.pushEvent(new InnerEventDTO()
                .setGlobalUid(StringUtils.joinWith("_", EventTypeConstants.MARKET_AMM_CLOSED_EVENT, marketContext.getClosedEventLog().getLog().getTransactionHash()))
                .setName(CoreInnerEventConstants.MARKET_CLOSE_SUCCESS)
                .addBody(CoreInnerEventConstants.MARKET, marketDO)
                .addBody(CoreInnerEventConstants.EVENT, marketContext.getClosedEventLog())
        );

        // 发送状态变更通知
        marketStatusNotifier.notifyMarketStatusChanged(marketDO, ZeekConstants.MarketStatus.Running, ZeekConstants.MarketStatus.Ended);

        log.info("Market closed successfully: {}", marketDO.getConditionId());
        return true;
    }
}
