package com.zeek.core.server.business.reference.executor.impl.wallet;

import co.evg.scaffold.innerEvent.common.annotation.ExecutorTopics;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kikitrade.framework.common.util.ChainIdUtil;
import com.zeek.core.common.constants.CoreInnerEventConstants;
import com.zeek.core.common.constants.ZeekConstants;
import com.zeek.core.contract.model.event.prediction.MarketAMMFundingAddedEventLog;
import com.zeek.core.dal.ots.model.PredictionMarketsDO;
import com.zeek.core.dal.ots.model.WalletTransactionDO;
import com.zeek.core.server.business.reference.service.IPriceReference;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: xiang.bai
 * @description:
 * @create: 2024-08-29
 **/
@Component
@Slf4j
@ExecutorTopics(value = {CoreInnerEventConstants.WALLET_TRANSACTION})
public class PredictionOutcomeInnerExecutor extends AbstractWalletTransactionInnerExecutor<MarketAMMFundingAddedEventLog> {

    @Resource
    IPriceReference priceReference;

    @Override
    public String getExecutorEventType() {
        return CoreInnerEventConstants.WALLET_TRANSACTION_PREDICTION_OUTCOME;
    }

    @Override
    public List<WalletTransactionDO> assembleWalletTransactionDO(MarketAMMFundingAddedEventLog eventLog, JSONObject input) {
        log.info("PredictionOutcomeInnerExecutor assembleWalletTransactionDO, eventLog: {}, input: {}", JSON.toJSONString(eventLog), JSON.toJSONString(input));
        String transactionHash = eventLog.getLog().getTransactionHash();
        String address = input.getString(CoreInnerEventConstants.AA_ADDRESS);
        PredictionMarketsDO marketsDO = input.getObject(CoreInnerEventConstants.MARKET, PredictionMarketsDO.class);

        WalletTransactionDO walletTransactionDO = new WalletTransactionDO();
        walletTransactionDO.setType(ZeekConstants.WalletTransactionType.PREDICTION_OUTCOME);
        walletTransactionDO.setCategory(ZeekConstants.WalletTransactionCategory.PREDICTION_OUTCOME);
        walletTransactionDO.setDirection(ZeekConstants.WalletTransactionDirection.SEND.getCode());
        walletTransactionDO.setAddress(address);
        walletTransactionDO.setChainId(ChainIdUtil.getChainId());
        walletTransactionDO.setTimestamp(System.currentTimeMillis());
        walletTransactionDO.setUniqueId(ZeekConstants.WalletTransactionType.PREDICTION_OUTCOME, transactionHash);
        walletTransactionDO.setTransactionHash(transactionHash);
        walletTransactionDO.setTitle("Pay for Answer");
        walletTransactionDO.setToken(marketsDO.getToken());
        walletTransactionDO.setValue(eventLog.getFundingChange().toString());
        walletTransactionDO.setSymbol(getTokenSymbol(marketsDO.getToken()));
        String usdValue = priceReference.exchangeUSD(getTokenSymbol(marketsDO.getToken()), eventLog.getFundingChange().toString());
        if (StringUtils.isNotBlank(usdValue)) {
            walletTransactionDO.setValueUsd(usdValue);
        }
        walletTransactionDO.setCreated(System.currentTimeMillis());
        walletTransactionDO.setModified(System.currentTimeMillis());

        return List.of(walletTransactionDO);
    }

    @Override
    protected Class<MarketAMMFundingAddedEventLog> getEventLogClass() {
        return MarketAMMFundingAddedEventLog.class;
    }

}
