package com.zeek.core.server.business.prediction.service;

import com.zeek.core.api.*;
import com.zeek.core.common.constants.ZeekConstants;
import com.zeek.core.common.exception.BaseException;
import com.zeek.core.contract.model.event.prediction.MarketAMMClaimedEventLog;
import com.zeek.core.contract.model.event.prediction.MarketAMMClosedEventLog;
import com.zeek.core.contract.model.event.prediction.MarketAMMCreatedEventLog;
import com.zeek.core.contract.model.event.prediction.MarketAMMFeeChangedEventLog;
import com.zeek.core.server.business.prediction.model.MarketActionContext;

import java.io.IOException;
import java.math.BigInteger;
import java.util.List;
import java.util.Map;

/**
 * @author: lizhifeng
 * @date: 2025/5/15 19:03
 */
public interface PredictionService {

    PredictionConfigDTO configs();

    PredictionMarketDTO createMarket(CreatMarketRequest request) throws BaseException;

    /**
     * 处理市场上链确认
     */
    boolean handleMarketOnChain(MarketAMMCreatedEventLog lmsrMarketCreationEventLog) throws BaseException;

    /**
     * 完成市场
     */
    boolean handleCloseMarketOnChain(MarketAMMClosedEventLog event) throws BaseException;

    boolean handleClaimOnChain(MarketAMMClaimedEventLog event) throws BaseException;

    /**
     * 获取市场状态
     */
    String getMarketStatus(String conditionId) throws BaseException;

    /**
     * 获取市场详情
     *
     * @param conditionId 市场ID
     * @param customerId 用户ID，用于查询用户是否参与过该市场
     * @return 市场详情
     * @throws BaseException 异常信息
     */
    PredictionMarketDTO getMarketDetail(String conditionId, String customerId) throws BaseException;

    /**
     * 获取市场列表
     */
    PredictionMarketListDTO markets(MarketsRequest request) throws BaseException;

    boolean feeChanged(MarketAMMFeeChangedEventLog event) throws BaseException;

    VoteResponse vote(VoteRequest request) throws BaseException;

    boolean updateFunding(String conditionId, BigInteger funding, BigInteger timestamp);


    boolean expiredPrediction(MarketActionContext context) throws BaseException;

    CloseMarketResponse closeMarket(CloseMarketRequest request) throws BaseException, IOException, IllegalAccessException;

    boolean settleMarket(String marketId) throws BaseException;

    Map<String, ZeekConstants.PredictionClaimStatus> queryClaimStatus(String customerId, List<String> marketId) throws BaseException;
}
