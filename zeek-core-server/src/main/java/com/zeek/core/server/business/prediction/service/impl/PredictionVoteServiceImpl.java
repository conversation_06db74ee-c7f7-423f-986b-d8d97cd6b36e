package com.zeek.core.server.business.prediction.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alicloud.openservices.tablestore.model.Column;
import com.alicloud.openservices.tablestore.model.ColumnValue;
import com.kikitrade.kseq.api.SeqClient;
import com.zeek.core.api.VoteRequest;
import com.zeek.core.api.VoteResponse;
import com.zeek.core.api.respond.ZeekCoreRespondCode;
import com.zeek.core.common.constants.CacheConstants;
import com.zeek.core.common.constants.ZeekConstants;
import com.zeek.core.common.exception.BaseException;
import com.zeek.core.dal.ots.builder.*;
import com.zeek.core.dal.ots.model.*;
import com.zeek.core.server.business.prediction.service.PredictionParticipantService;
import com.zeek.core.server.business.prediction.service.PredictionVoteService;
import com.zeek.core.server.business.reference.service.IQuestsReference;
import com.zeek.core.server.common.seq.VoteSeqRuleBuilder;
import com.zeek.core.server.configuration.PredictionMarketProperties;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.zeek.core.api.respond.ZeekCoreRespondCode.MARKET_RESULT_FAILED;
import static com.zeek.core.common.constants.ZeekConstants.MarketStatus.Closed;
import static com.zeek.core.common.constants.ZeekConstants.MarketStatus.Ended;
import static com.zeek.core.common.constants.ZeekConstants.WalletTransactionDirection.SEND;
import static com.zeek.core.server.common.constants.QuestsConstants.PointBusinessType.PREDICTION_VOTE_CONSUME;

/**
 * 市场投票结果服务实现
 *
 * @author: AI Assistant
 * @date: 2025/1/27
 */
@Service
@Slf4j
public class PredictionVoteServiceImpl implements PredictionVoteService {

    @Resource
    PredictionMarketProperties predictionMarketProperties;
    @Resource
    SeqClient seqClient;
    @Resource
    PredictionParticipantService participantService;
    @Resource
    IQuestsReference questsReference;
    @Resource
    PredictionMarketsBuilder predictionMarketsBuilder;
    @Resource
    PredictionOutcomesBuilder outcomesBuilder;
    @Autowired
    private PredictionVotesBuilder predictionVotesBuilder;
    @Resource
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 缓存过期时间：7天
     */
    private static final long CACHE_EXPIRE_DAYS = 3;

    @Override
    public VoteResponse vote(VoteRequest request) throws BaseException {
        VoteResponse.Builder voteResponse = VoteResponse.newBuilder();
        PredictionMarketsDO marketDO = predictionMarketsBuilder.getRowByConditionId(request.getMarketId());
        if (marketDO == null) {
            log.error("Market not found: {}", request.getMarketId());
            throw new BaseException(ZeekCoreRespondCode.MARKET_NOT_FOUND);
        }
        List<PredictionOutcomesDO> outcomesDOS = outcomesBuilder.getEffectiveRows(request.getMarketId());
        if (outcomesDOS.size() <= 1) {
            throw new BaseException(ZeekCoreRespondCode.MARKET_SLOT_NOT_ENOUGH);
        }
        Double myVote = predictionVotesBuilder.getAnswerVoteByCustomerId(request.getMarketId(), request.getSlot(), request.getCustomerId());
        if (myVote > 0) {
            throw new BaseException(ZeekCoreRespondCode.MARKET_ALREADY_EXIST);
        }
        if (request.getAmount() > predictionMarketProperties.getVote().getMax() || request.getAmount() < predictionMarketProperties.getVote().getMin()) {
            throw new BaseException(ZeekCoreRespondCode.PARAM_ILLEGAL);
        }
        String businessId = seqClient.next(VoteSeqRuleBuilder.rule());
        boolean deductPoint = questsReference.deductPoint(request.getCustomerId(), request.getAmount(), businessId, PREDICTION_VOTE_CONSUME, PREDICTION_VOTE_CONSUME.getDesc());
        if (deductPoint) {
            PredictionVotesDO predictionVotesDO = new PredictionVotesDO();
            predictionVotesDO.setId(businessId);
            predictionVotesDO.setCreated(System.currentTimeMillis());
            predictionVotesDO.setChainId(marketDO.getChainId());
            predictionVotesDO.setSlot(request.getSlot());
            predictionVotesDO.setValue(request.getAmount());
            predictionVotesDO.setDirection(SEND.getCode());
            predictionVotesDO.setModified(System.currentTimeMillis());
            predictionVotesDO.setMarketId(request.getMarketId());
            predictionVotesDO.setCustomerId(request.getCustomerId());
            predictionVotesBuilder.putRow(predictionVotesDO);
            participantService.participate(request.getCustomerId(), request.getMarketId(), ZeekConstants.PredictionAction.VOTE.name());
            voteResponse.setSuccess(true)
                    .setSlot(request.getSlot())
                    .setAmount(request.getAmount());
            predictionMarketsBuilder.updateAndIncRow(marketDO, null, List.of(new Column("vote", ColumnValue.fromLong(request.getAmount()))), null, null);

        } else {
            voteResponse.setSuccess(false);
        }
        return voteResponse.build();
    }

    @Override
    public List<BigInteger> getCachedVoteResult(String marketId) {
        if (StringUtils.isBlank(marketId)) {
            return null;
        }

        try {
            String cacheKey = CacheConstants.getMarketVoteResultKey(marketId);
            String cachedResult = redisTemplate.opsForValue().get(cacheKey);

            if (StringUtils.isNotBlank(cachedResult)) {
                List<BigInteger> result = JSON.parseObject(cachedResult, new TypeReference<List<BigInteger>>() {
                });
                log.info("Found cached vote result for market: {}, result: {}", marketId, result);
                return result;
            }
        } catch (Exception e) {
            log.error("Failed to get cached vote result for market: {}", marketId, e);
        }

        return null;
    }

    @Override
    public List<BigInteger> calculateAndCacheVoteResult(String marketId, int outcomeCount) throws BaseException {
        if (StringUtils.isBlank(marketId) || outcomeCount <= 0) {
            log.warn("Invalid parameters: marketId={}, outcomeCount={}", marketId, outcomeCount);
            return Collections.emptyList();
        }

        try {
            // 查询每个slot的投票数量
            List<Double> voteAmounts = new ArrayList<>();
            for (int slot = 0; slot < outcomeCount; slot++) {
                Double voteAmount = predictionVotesBuilder.getAnswerVote(marketId, slot);
                voteAmounts.add(voteAmount != null ? voteAmount : 0.0);
                log.info("Market: {}, Slot: {}, Vote Amount: {}", marketId, slot, voteAmount);
            }

            // 找出最大投票数
            double maxVoteAmount = voteAmounts.stream()
                    .mapToDouble(Double::doubleValue)
                    .max()
                    .orElse(0.0);

            // 生成结果数组：投票数等于最大值的为获胜者(1)，其他为失败者(0)
            List<BigInteger> result = new ArrayList<>();
            for (double voteAmount : voteAmounts) {
                // 使用精度比较，避免浮点数精度问题
                if (Math.abs(voteAmount - maxVoteAmount) < 0.0001) {
                    result.add(BigInteger.valueOf(1)); // 获胜
                } else {
                    result.add(BigInteger.valueOf(0)); // 失败
                }
            }

            // 缓存结果
            cacheVoteResult(marketId, result);

            log.info("Calculated vote result for market: {}, vote amounts: {}, max: {}, result: {}",
                    marketId, voteAmounts, maxVoteAmount, result);

            return result;
        } catch (Exception e) {
            log.error("Failed to calculate vote result for market: {}", marketId, e);
            throw new BaseException(MARKET_RESULT_FAILED);
        }
    }

    @Override
    public List<BigInteger> getMarketVoteResult(String marketId) throws BaseException {
        // 参数校验
        if (StringUtils.isBlank(marketId)) {
            throw new BaseException(ZeekCoreRespondCode.PARAM_ILLEGAL);
        }
        // 检查市场是否存在
        PredictionMarketsDO marketDO = predictionMarketsBuilder.getRowByConditionId(marketId);
        if (marketDO == null) {
            log.error("Market not found: {}", marketId);
            throw new BaseException(ZeekCoreRespondCode.MARKET_NOT_FOUND);
        }
        if (!Ended.name().equals(marketDO.getStatus()) && !Closed.name().equals(marketDO.getStatus())) {
            log.error("Market status not ended: {}", marketId);
            throw new BaseException(ZeekCoreRespondCode.MARKET_NOT_ENDED);
        }

        // 先尝试从缓存获取结果
        List<BigInteger> cachedResult = this.getCachedVoteResult(marketId);
        if (cachedResult != null) {
            log.info("Found cached vote result for market: {}, result: {}", marketId, cachedResult);
            return cachedResult;
        }

        // 如果缓存中没有，则重新计算
        List<PredictionOutcomesDO> outcomesDOS = outcomesBuilder.getEffectiveRows(marketId);
        int outcomeCount = outcomesDOS.size();

        if (outcomeCount == 0) {
            log.warn("No outcomes found for market: {}", marketId);
            return Collections.emptyList();
        }

        // 计算并缓存结果
        List<BigInteger> result = this.calculateAndCacheVoteResult(marketId, outcomeCount);

        log.info("Calculated vote result for market: {}, result: {}", marketId, result);
        return result;
    }

    @Override
    public void clearVoteResultCache(String marketId) {
        if (StringUtils.isBlank(marketId)) {
            return;
        }

        try {
            String cacheKey = CacheConstants.getMarketVoteResultKey(marketId);
            redisTemplate.delete(cacheKey);
            log.info("Cleared vote result cache for market: {}", marketId);
        } catch (Exception e) {
            log.error("Failed to clear vote result cache for market: {}", marketId, e);
        }
    }

    @Override
    public Long winPoint(String marketId, String customerId) throws BaseException {
        Double addPoint = predictionVotesBuilder.getMarketSettle(marketId, customerId);
        if (addPoint != null && addPoint > 0) {
            return addPoint.longValue();
        }

        // TODO
        List<BigInteger> voteResults = this.getMarketVoteResult(marketId);
        List<Integer> winnerSlots = new ArrayList<>();
        List<Integer> losingSlots = new ArrayList<>();
        for (int i = 0; i < voteResults.size(); i++) {
            if (BigInteger.ONE.equals(voteResults.get(i))) {
                winnerSlots.add(i);
            } else {
                losingSlots.add(i);
            }
        }
        Double userVote = predictionVotesBuilder.getAnswerVoteByCustomerId(marketId, winnerSlots, customerId);
        if (userVote == null || userVote <= 0) {
            return 0L;
        }

        Double winPoint = predictionVotesBuilder.getAnswerVote(marketId, winnerSlots);
        Double losePoint = predictionVotesBuilder.getAnswerVote(marketId, losingSlots);

        BigDecimal userVoteValue = new BigDecimal(userVote);
        BigDecimal winPointDecimal = new BigDecimal(winPoint);
        BigDecimal losePointDecimal = new BigDecimal(losePoint);

        // amount = (votesDO.getValue()/winPoint) * losePoint + votesDO.getValue()
        BigDecimal amount = userVoteValue.divide(winPointDecimal, 10, BigDecimal.ROUND_HALF_UP)
                .multiply(losePointDecimal)
                .add(userVoteValue);

        // 向下取整转为整数
        Long finalAmount = amount.longValue();
        return finalAmount;
    }

    /**
     * 缓存投票结果
     *
     * @param marketId 市场ID
     * @param result   投票结果
     */
    private void cacheVoteResult(String marketId, List<BigInteger> result) {
        try {
            String cacheKey = CacheConstants.getMarketVoteResultKey(marketId);
            String resultJson = JSON.toJSONString(result);
            redisTemplate.opsForValue().set(cacheKey, resultJson, CACHE_EXPIRE_DAYS, TimeUnit.DAYS);
            log.info("Cached vote result for market: {}, result: {}", marketId, result);
        } catch (Exception e) {
            log.error("Failed to cache vote result for market: {}", marketId, e);
        }
    }
}
