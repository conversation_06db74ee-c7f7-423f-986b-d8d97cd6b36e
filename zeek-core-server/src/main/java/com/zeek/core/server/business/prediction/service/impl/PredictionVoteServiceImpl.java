package com.zeek.core.server.business.prediction.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.zeek.core.api.respond.ZeekCoreRespondCode;
import com.zeek.core.common.constants.CacheConstants;
import com.zeek.core.common.constants.ZeekConstants;
import com.zeek.core.common.exception.BaseException;
import com.zeek.core.dal.ots.builder.PredictionMarketsBuilder;
import com.zeek.core.dal.ots.builder.PredictionOutcomesBuilder;
import com.zeek.core.dal.ots.builder.PredictionVotesBuilder;
import com.zeek.core.dal.ots.model.PredictionMarketsDO;
import com.zeek.core.dal.ots.model.PredictionOutcomesDO;
import com.zeek.core.server.business.prediction.service.PredictionVoteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.zeek.core.api.respond.ZeekCoreRespondCode.MARKET_RESULT_FAILED;
import static com.zeek.core.common.constants.ZeekConstants.MarketStatus.Closed;
import static com.zeek.core.common.constants.ZeekConstants.MarketStatus.Ended;

/**
 * 市场投票结果服务实现
 *
 * @author: AI Assistant
 * @date: 2025/1/27
 */
@Service
@Slf4j
public class PredictionVoteServiceImpl implements PredictionVoteService {

    @Autowired
    private PredictionVotesBuilder predictionVotesBuilder;

    @Resource
    private PredictionMarketsBuilder predictionMarketsBuilder;

    @Resource
    private PredictionOutcomesBuilder predictionOutcomesBuilder;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 缓存过期时间：7天
     */
    private static final long CACHE_EXPIRE_DAYS = 3;

    @Override
    public List<BigInteger> getCachedVoteResult(String marketId) {
        if (StringUtils.isBlank(marketId)) {
            log.warn("Market ID is blank when getting cached vote result");
            return null;
        }

        try {
            String cacheKey = CacheConstants.getMarketVoteResultKey(marketId);
            String cachedResult = redisTemplate.opsForValue().get(cacheKey);

            if (StringUtils.isNotBlank(cachedResult)) {
                List<BigInteger> result = JSON.parseObject(cachedResult, new TypeReference<List<BigInteger>>() {});
                log.info("Found cached vote result for market: {}, result: {}", marketId, result);
                return result;
            }

            log.debug("No cached vote result found for market: {}", marketId);
        } catch (Exception e) {
            log.error("Failed to get cached vote result for market: {}, error: {}", marketId, e.getMessage(), e);
        }

        return null;
    }

    @Override
    public List<BigInteger> calculateAndCacheVoteResult(String marketId, int outcomeCount) throws BaseException {
        if (StringUtils.isBlank(marketId) || outcomeCount <= 0) {
            log.error("Invalid parameters for calculating vote result: marketId={}, outcomeCount={}", marketId, outcomeCount);
            throw new BaseException(ZeekCoreRespondCode.PARAM_ILLEGAL);
        }

        try {
            log.info("Calculating vote result for market: {}, outcomeCount: {}", marketId, outcomeCount);

            // 查询每个slot的投票数量
            List<Double> voteAmounts = new ArrayList<>();
            for (int slot = 0; slot < outcomeCount; slot++) {
                Double voteAmount = predictionVotesBuilder.getAnswerVote(marketId, slot);
                double amount = voteAmount != null ? voteAmount : 0.0;
                voteAmounts.add(amount);
                log.debug("Market: {}, Slot: {}, Vote Amount: {}", marketId, slot, amount);
            }

            // 找出最大投票数
            double maxVoteAmount = voteAmounts.stream()
                    .mapToDouble(Double::doubleValue)
                    .max()
                    .orElse(0.0);

            // 生成结果数组：投票数等于最大值的为获胜者(1)，其他为失败者(0)
            List<BigInteger> result = new ArrayList<>();
            for (double voteAmount : voteAmounts) {
                // 使用精度比较，避免浮点数精度问题
                if (Math.abs(voteAmount - maxVoteAmount) < 0.0001 && maxVoteAmount > 0) {
                    result.add(BigInteger.valueOf(1)); // 获胜
                } else {
                    result.add(BigInteger.valueOf(0)); // 失败
                }
            }

            // 缓存结果
            cacheVoteResult(marketId, result);

            log.info("Successfully calculated vote result for market: {}, vote amounts: {}, max: {}, result: {}",
                    marketId, voteAmounts, maxVoteAmount, result);

            return result;
        } catch (BaseException e) {
            log.error("Business exception while calculating vote result for market: {}, error: {}", marketId, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error while calculating vote result for market: {}, error: {}", marketId, e.getMessage(), e);
            throw new BaseException(MARKET_RESULT_FAILED);
        }
    }

    @Override
    public List<BigInteger> getMarketVoteResult(String marketId) throws BaseException {
        log.info("Getting market vote result for marketId: {}", marketId);

        // 参数校验
        if (StringUtils.isBlank(marketId)) {
            log.error("Market ID is blank");
            throw new BaseException(ZeekCoreRespondCode.PARAM_ILLEGAL);
        }

        try {
            // 检查市场是否存在
            PredictionMarketsDO marketDO = predictionMarketsBuilder.getRowByConditionId(marketId);
            if (marketDO == null) {
                log.error("Market not found: {}", marketId);
                throw new BaseException(ZeekCoreRespondCode.MARKET_NOT_FOUND);
            }

            // 检查市场状态
            if (!Ended.name().equals(marketDO.getStatus()) && !Closed.name().equals(marketDO.getStatus())) {
                log.error("Market status not ended: {}, current status: {}", marketId, marketDO.getStatus());
                throw new BaseException(ZeekCoreRespondCode.MARKET_NOT_ENDED);
            }

            // 先尝试从缓存获取结果
            List<BigInteger> cachedResult = getCachedVoteResult(marketId);
            if (cachedResult != null) {
                log.info("Found cached vote result for market: {}, result: {}", marketId, cachedResult);
                return cachedResult;
            }

            // 如果缓存中没有，则重新计算
            List<PredictionOutcomesDO> outcomesDOS = predictionOutcomesBuilder.getEffectiveRows(marketId);
            int outcomeCount = outcomesDOS.size();

            if (outcomeCount == 0) {
                log.warn("No outcomes found for market: {}", marketId);
                return Collections.emptyList();
            }

            // 计算并缓存结果
            List<BigInteger> result = calculateAndCacheVoteResult(marketId, outcomeCount);

            log.info("Calculated vote result for market: {}, result: {}", marketId, result);
            return result;

        } catch (BaseException e) {
            log.error("Business exception while getting market vote result for marketId: {}", marketId, e);
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error while getting market vote result for marketId: {}", marketId, e);
            throw new BaseException(MARKET_RESULT_FAILED);
        }
    }

    @Override
    public void clearVoteResultCache(String marketId) {
        if (StringUtils.isBlank(marketId)) {
            log.warn("Market ID is blank when clearing vote result cache");
            return;
        }

        try {
            String cacheKey = CacheConstants.getMarketVoteResultKey(marketId);
            Boolean deleted = redisTemplate.delete(cacheKey);
            if (Boolean.TRUE.equals(deleted)) {
                log.info("Successfully cleared vote result cache for market: {}", marketId);
            } else {
                log.debug("No cache found to clear for market: {}", marketId);
            }
        } catch (Exception e) {
            log.error("Failed to clear vote result cache for market: {}, error: {}", marketId, e.getMessage(), e);
        }
    }

    /**
     * 缓存投票结果
     *
     * @param marketId 市场ID
     * @param result 投票结果
     */
    private void cacheVoteResult(String marketId, List<BigInteger> result) {
        if (StringUtils.isBlank(marketId) || result == null) {
            log.warn("Invalid parameters for caching vote result: marketId={}, result={}", marketId, result);
            return;
        }

        try {
            String cacheKey = CacheConstants.getMarketVoteResultKey(marketId);
            String resultJson = JSON.toJSONString(result);
            redisTemplate.opsForValue().set(cacheKey, resultJson, CACHE_EXPIRE_DAYS, TimeUnit.DAYS);
            log.info("Successfully cached vote result for market: {}, result: {}, expiry: {} days",
                    marketId, result, CACHE_EXPIRE_DAYS);
        } catch (Exception e) {
            log.error("Failed to cache vote result for market: {}, result: {}, error: {}",
                    marketId, result, e.getMessage(), e);
        }
    }
}
