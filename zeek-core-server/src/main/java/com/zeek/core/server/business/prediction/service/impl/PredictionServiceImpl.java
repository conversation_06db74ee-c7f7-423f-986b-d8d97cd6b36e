package com.zeek.core.server.business.prediction.service.impl;

import co.evg.scaffold.innerEvent.client.InnerEventClient;
import co.evg.scaffold.innerEvent.common.model.InnerEventDTO;
import com.alibaba.fastjson.JSON;
import com.alicloud.openservices.tablestore.model.Column;
import com.alicloud.openservices.tablestore.model.ColumnValue;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.framework.common.util.ChainIdUtil;
import com.kikitrade.kseq.api.SeqClient;
import com.zeek.core.api.*;
import com.zeek.core.api.respond.ZeekCoreRespondCode;
import com.zeek.core.common.constants.CoreInnerEventConstants;
import com.zeek.core.common.constants.ZeekConstants;
import com.zeek.core.common.constants.ZeekConstants.MarketEvent;
import com.zeek.core.common.constants.ZeekConstants.MarketStatus;
import com.zeek.core.common.exception.BaseException;
import com.zeek.core.common.statemachine.StateMachine;
import com.zeek.core.common.util.ContractUtils;
import com.zeek.core.contract.common.EventTypeConstants;
import com.zeek.core.contract.model.event.prediction.MarketAMMClaimedEventLog;
import com.zeek.core.contract.model.event.prediction.MarketAMMClosedEventLog;
import com.zeek.core.contract.eip712.PredictionCloseSignatureUtils;
import com.zeek.core.contract.eip712.message.PayoutDataTypedMessage;
import com.zeek.core.contract.model.event.prediction.MarketAMMCreatedEventLog;
import com.zeek.core.contract.model.event.prediction.MarketAMMFeeChangedEventLog;
import com.zeek.core.dal.ots.builder.*;
import com.zeek.core.dal.ots.model.PredictionMarketsDO;
import com.zeek.core.dal.ots.model.PredictionOutcomesDO;
import com.zeek.core.dal.ots.model.PredictionPositionsDO;
import com.zeek.core.dal.ots.model.PredictionTransactionsDO;
import com.zeek.core.dal.ots.model.PredictionVotesDO;
import com.zeek.core.dal.ots.model.ProfileBasicDO;
import com.zeek.core.server.business.contract.siger.ISigner;
import com.zeek.core.server.business.contract.siger.KmsQuestVerifySigner;
import com.zeek.core.server.business.prediction.action.ActiveMarketEventAction;
import com.zeek.core.server.business.prediction.action.CreateMarketEventAction;
import com.zeek.core.server.business.prediction.action.ClosedMarketEventAction;
import com.zeek.core.server.business.prediction.action.ExpiredMarketEventAction;
import com.zeek.core.server.business.prediction.model.MarketActionContext;
import com.zeek.core.server.business.prediction.model.PredictionModelConvert;
import com.zeek.core.server.business.prediction.service.PredictionParticipantService;
import com.zeek.core.server.business.prediction.service.PredictionPositionsService;
import com.zeek.core.server.business.prediction.service.PredictionVoteService;
import com.zeek.core.server.business.prediction.service.PredictionService;
import com.zeek.core.server.business.profile.model.GetAggProfileContext;
import com.zeek.core.server.business.profile.service.ProfileService;
import com.zeek.core.server.business.reference.service.IQuestsReference;
import com.zeek.core.server.common.constants.QuestsConstants;
import com.zeek.core.server.common.seq.VoteSeqRuleBuilder;
import com.zeek.core.server.configuration.PredictionMarketProperties;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.web3j.abi.datatypes.generated.Bytes32;
import org.web3j.crypto.Hash;
import org.web3j.utils.Numeric;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.zeek.core.common.constants.ZeekConstants.MarketEvent.ZEEK_EXPIRED_MARKET;
import static com.zeek.core.common.constants.ZeekConstants.MarketStatus.Closed;
import static com.zeek.core.common.constants.ZeekConstants.MarketStatus.Ended;
import static com.zeek.core.common.constants.ZeekConstants.PredictionClaimStatus.CLAIMED;
import static com.zeek.core.common.constants.ZeekConstants.WalletTransactionDirection.RECEIVE;
import static com.zeek.core.common.constants.ZeekConstants.WalletTransactionDirection.SEND;
import static com.zeek.core.server.common.constants.QuestsConstants.PointBusinessType.PREDICTION_VOTE_CONSUME;
import static com.zeek.core.server.common.constants.QuestsConstants.PointBusinessType.PREDICTION_VOTE_WIN;

/**
 * @author: lizhifeng
 * @date: 2025/5/15 19:04
 */
@Service
@Slf4j
public class PredictionServiceImpl implements PredictionService {

    @Resource
    Map<String, ISigner> signers;
    @Resource
    PredictionMarketProperties predictionMarketProperties;
    @Resource
    PredictionMarketsBuilder predictionMarketsBuilder;
    @Resource
    PredictionParticipantsBuilder predictionParticipantsBuilder;
    @Resource
    StateMachine<MarketStatus, MarketEvent> stateMachine;
    @Resource
    KmsQuestVerifySigner kmsQuestVerifySigner;
    @Resource
    PredictionOutcomesBuilder outcomesBuilder;
    @Resource
    PredictionParticipantService participantService;
    @Resource
    ProfileBasicBuilder profileBasicBuilder;
    @Resource
    PredictionTransactionsBuilder predictionTransactionsBuilder;
    @Resource
    private PredictionPositionsBuilder positionsBuilder;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    SeqClient seqClient;
    @Resource
    IQuestsReference questsReference;
    @Autowired
    private PredictionVotesBuilder predictionVotesBuilder;
    @Resource
    ProfileService profileService;
    @Resource
    private PredictionVoteService marketVoteResultService;
    @Resource
    InnerEventClient innerEventClient;

    @PostConstruct
    void buildStateMachine() {
        stateMachine.accept(null, MarketEvent.ZEEK_CREATE_MARKET, CreateMarketEventAction.class);
        stateMachine.accept(MarketStatus.Pending, MarketEvent.ZEEK_ISSUED_MARKET_ON_CHAIN, ActiveMarketEventAction.class);

        stateMachine.accept(MarketStatus.Running, ZEEK_EXPIRED_MARKET, ExpiredMarketEventAction.class);

        stateMachine.accept(Ended, MarketEvent.ZEEK_CLOSED_MARKET, ClosedMarketEventAction.class);
    }

    @Override
    public PredictionConfigDTO configs() {
        if (predictionMarketProperties == null) {
            return null;
        }

        PredictionConfigDTO.Builder builder = PredictionConfigDTO.newBuilder();
        List<PredictionMarketProperties.TokenConfig> tokens = predictionMarketProperties.getTokens();
        if (CollectionUtils.isNotEmpty(tokens)) {
            tokens.forEach(tokenConfig -> {
                PredictionTokenConfig.Builder tokenBuilder = PredictionTokenConfig.newBuilder()
                        .setName(tokenConfig.getName())
                        .setSymbol(tokenConfig.getSymbol())
                        .setAddress(tokenConfig.getAddress())
                        .setCollateralMin(tokenConfig.getCollateralMin())
                        .setCollateralMax(tokenConfig.getCollateralMax())
                        .setSlotUnit(tokenConfig.getSlotUnit());
                builder.addTokens(tokenBuilder.build());
            });
        }

        PredictionMarketProperties.DaysConfig days = predictionMarketProperties.getDays();
        if (days != null) {
            PredictionDaysConfig.Builder daysBuilder = PredictionDaysConfig.newBuilder()
                    .setMin(days.getMin())
                    .setMax(days.getMax());
            builder.setDays(daysBuilder.build());
        }

        PredictionMarketProperties.FeeConfig fee = predictionMarketProperties.getFee();
        if (fee != null) {
            PredictionFeeConfig.Builder feeBuilder = PredictionFeeConfig.newBuilder()
                    .setTotal(fee.getTotal())
                    .setPlatform(fee.getPlatform())
                    .setPoster(fee.getPoster())
                    .setAnswer(fee.getAnswer());
            builder.setFee(feeBuilder.build());
        }

        PredictionMarketProperties.VoteConfig vote = predictionMarketProperties.getVote();
        if (vote != null) {
            PredictionVoteConfig.Builder voteBuilder = PredictionVoteConfig.newBuilder()
                    .setMin(vote.getMin())
                    .setMax(vote.getMax())
                    .addAllOptions(vote.getOptions());
            builder.setVote(voteBuilder.build());
        }

        return builder.build();
    }

    @Override
    public PredictionMarketDTO createMarket(CreatMarketRequest request) throws BaseException {
        try {
            // 创建上下文
            String questionId = Numeric.toHexString(Hash.sha3(request.getTitle().getBytes()));
            String oracle = kmsQuestVerifySigner.getSignerAddress();
            Long slot = request.getSlot();
            String conditionId = ContractUtils.getConditionId(oracle, questionId, BigInteger.valueOf(slot));

            PredictionMarketsDO marketDO = predictionMarketsBuilder.getRowByConditionId(conditionId);
            if (marketDO != null) {
                if (MarketStatus.Pending.name().equals(marketDO.getStatus()) && marketDO.getCustomerId().equals(request.getCustomerId())) {
                    return PredictionModelConvert.toMarketDTO(marketDO);
                }
                throw new BaseException(ZeekCoreRespondCode.MARKET_ALREADY_EXIST);
            }

            MarketActionContext context = MarketActionContext.builder()
                    .id(conditionId)
                    .conditionId(conditionId)
                    .customerId(request.getCustomerId())
                    .questionId(questionId)
                    .slot(slot)
                    .title(request.getTitle())
                    .content(request.getContent())
                    .medias(request.getMediasList())
                    .token(request.getToken())
                    .value(request.getValue())
                    .endTime(request.getEndTime())
                    .created(System.currentTimeMillis())
                    .build();

            // 触发状态机
            boolean result = stateMachine.action(null, MarketEvent.ZEEK_CREATE_MARKET, context);
            if (result) {
                // 获取创建的市场
                marketDO = predictionMarketsBuilder.getRowByConditionId(context.getConditionId());

                // 转换为DTO返回
                return PredictionModelConvert.toMarketDTO(marketDO);
            }
        } catch (BaseException e) {
            log.error("Failed to create market", e);
            throw e;
        }

        return null;
    }

    @Override
    public boolean handleMarketOnChain(MarketAMMCreatedEventLog marketAMMCreatedEventLog) throws BaseException {
        // 将字节数组转换为16进制字符串
        StringBuilder hexString = new StringBuilder("0x");
        for (byte b : marketAMMCreatedEventLog.getConditionId()) {
            String hex = Integer.toHexString(b & 0xFF);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        String conditionId = hexString.toString();

        PredictionMarketsDO marketDO = predictionMarketsBuilder.getRowByConditionId(conditionId);
        if (marketDO == null) {
            log.error("Market not found: {}", conditionId);
            throw new BaseException(ZeekCoreRespondCode.MARKET_NOT_FOUND);
        }

        // 创建上下文
        MarketActionContext context = MarketActionContext.builder()
                .id(conditionId)
                .eventLog(marketAMMCreatedEventLog)
                .conditionId(marketDO.getConditionId())
                .customerId(marketDO.getCustomerId())
                .chainId(marketDO.getChainId())
                .marketAddress(marketAMMCreatedEventLog.getMarketAddress())
                .questionId(Arrays.toString(marketAMMCreatedEventLog.getQuestionId()))
                .slot(marketAMMCreatedEventLog.getOutcomeSlotCount().longValue())
                .fee(String.valueOf(marketAMMCreatedEventLog.getFee()))
                .creatorFee(String.valueOf(marketAMMCreatedEventLog.getCreatorFee()))
                .proposalFee(String.valueOf(marketAMMCreatedEventLog.getProposalFee()))
                .token(marketAMMCreatedEventLog.collateralToken)
                .adminAddress(marketAMMCreatedEventLog.getAdminAddress())
                .financeAddress(marketAMMCreatedEventLog.getFinanceAddress())
                .oracleAddress(marketAMMCreatedEventLog.getOracleAddress())
                .collateralUnit(marketAMMCreatedEventLog.getCollateralUnit())
                .build();

        // 触发状态机
        return stateMachine.action(MarketStatus.get(marketDO.getStatus()),
                MarketEvent.ZEEK_ISSUED_MARKET_ON_CHAIN,
                context);
    }

    @Override
    public boolean handleCloseMarketOnChain(MarketAMMClosedEventLog event) throws BaseException {
        String marketAddress = event.getMarketAddress();
        List<BigInteger> payouts = event.getPayouts();
        log.info("prediction handleCloseMarketOnChain: {}", event);

        // 获取市场记录
        PredictionMarketsDO marketDO = predictionMarketsBuilder.getByMarketAddress(marketAddress);
        if (marketDO == null) {
            log.error("Market not found: {}", marketAddress);
            throw new BaseException(ZeekCoreRespondCode.MARKET_NOT_FOUND);
        }

        // 创建上下文
        MarketActionContext context = MarketActionContext.builder()
                .id(marketDO.getConditionId())
                .conditionId(marketDO.getConditionId())
                .payouts(payouts.stream().map(BigInteger::intValue).collect(Collectors.toList()))
                .closedEventLog(event)
                .build();
        // 触发状态机
        return stateMachine.action(MarketStatus.get(marketDO.getStatus()),
                MarketEvent.ZEEK_CLOSED_MARKET,
                context);
    }

    @Override
    public boolean handleClaimOnChain(MarketAMMClaimedEventLog eventLog) throws BaseException {
        log.info("prediction handleClaimOnChain: {}", eventLog);
        String marketAddress = eventLog.getMarketAddress();
        PredictionMarketsDO marketsDO = predictionMarketsBuilder.getByMarketAddress(marketAddress);
        if (marketsDO == null) {
            log.error("Market not found: {}", marketAddress);
            throw new BaseException(ZeekCoreRespondCode.MARKET_NOT_FOUND);
        }
        if (!MarketStatus.Closed.name().equals(marketsDO.getStatus())) {
            log.error("Market not closed: {}", marketAddress);
            throw new BaseException(ZeekCoreRespondCode.MARKET_NOT_CLOSED);
        }

        ProfileBasicDO profileDO = profileBasicBuilder.getByAddress(eventLog.getParticipant());

        innerEventClient.pushEvent(new InnerEventDTO()
                .setGlobalUid(StringUtils.joinWith("_", EventTypeConstants.MARKET_AMM_CLAIMED_EVENT, eventLog.getLog().getTransactionHash()))
                .setName(CoreInnerEventConstants.MARKET_CLAIM)
                .addBody(CoreInnerEventConstants.MARKET, marketsDO)
                .addBody(CoreInnerEventConstants.CUSTOMER_ID, profileDO.getCustomerId())
                .addBody(CoreInnerEventConstants.EVENT, eventLog));

        innerEventClient.pushEvent(new InnerEventDTO()
                .setGlobalUid(StringUtils.joinWith("_", EventTypeConstants.MARKET_AMM_CLAIMED_EVENT, eventLog.getLog().getTransactionHash()))
                .setName(CoreInnerEventConstants.WALLET_TRANSACTION)
                .addBody(CoreInnerEventConstants.WALLET_TRANSACTION_TYPE, CoreInnerEventConstants.WALLET_TRANSACTION_PREDICTION_CLAIM)
                .addBody(CoreInnerEventConstants.AA_ADDRESS, profileDO.getAddress())
                .addBody(CoreInnerEventConstants.MARKET, marketsDO)
                .addBody(CoreInnerEventConstants.EVENT, eventLog));
        return false;
    }


    @Override
    public String getMarketStatus(String conditionId) throws BaseException {
        // 获取市场记录
        PredictionMarketsDO marketDO = predictionMarketsBuilder.getRowByConditionId(conditionId);
        if (marketDO == null) {
            log.error("Market not found: {}", conditionId);
            throw new BaseException(ZeekCoreRespondCode.MARKET_NOT_FOUND);
        }

        return marketDO.getStatus();
    }

    @Override
    public PredictionMarketDTO getMarketDetail(String conditionId, String customerId) throws BaseException {
        // 获取市场记录
        PredictionMarketsDO marketDO = predictionMarketsBuilder.getRowByConditionId(conditionId);
        if (marketDO == null) {
            log.error("Market not found: {}", conditionId);
            throw new BaseException(ZeekCoreRespondCode.MARKET_NOT_FOUND);
        }

        // 1. 获取市场的outcomes
        List<PredictionOutcomesDO> marketOutcomes = outcomesBuilder.getEffectiveRows(conditionId);

        // 2. 按照volume转成BigDecimal做倒排序，当volume相同时按照created倒排
        marketOutcomes = marketOutcomes.stream()
                .sorted((o1, o2) -> {
                    // 先按volume排序（倒序）
                    BigDecimal volume1 = StringUtils.isNotBlank(o1.getVolume()) ? new BigDecimal(o1.getVolume()) : BigDecimal.ZERO;
                    BigDecimal volume2 = StringUtils.isNotBlank(o2.getVolume()) ? new BigDecimal(o2.getVolume()) : BigDecimal.ZERO;
                    int volumeCompare = volume2.compareTo(volume1); // 倒序

                    // 如果volume相同，则按created排序（倒序）
                    if (volumeCompare == 0) {
                        Long created1 = o1.getCreated() != null ? o1.getCreated() : 0L;
                        Long created2 = o2.getCreated() != null ? o2.getCreated() : 0L;
                        return created2.compareTo(created1); // 倒序
                    }

                    return volumeCompare;
                })
                .collect(Collectors.toList());
        List<String> customerIds = new ArrayList<>();
        customerIds.addAll(marketOutcomes.stream().map(PredictionOutcomesDO::getCustomerId).distinct().toList());
        customerIds.add(marketDO.getCustomerId());
        if (StringUtils.isNotBlank(customerId)) {
            customerIds.add(customerId);
        }
        GetAggProfileContext getAggProfileContext = new GetAggProfileContext();
        getAggProfileContext.setIdList(customerIds);
        getAggProfileContext.setNeedOpenSocialInfo(true);
        Map<String, ProfileDTO> profileDTOMap = profileService.getAggProfileMap(getAggProfileContext);
        // 3. 获取用户信息
        for (PredictionOutcomesDO outcomesDO : marketOutcomes) {
            // 只有结束的市场才返回总票数
            if (marketDO.getEndTime() < System.currentTimeMillis()) {
                Double answerVote = predictionVotesBuilder.getAnswerVote(outcomesDO.getMarketId(), outcomesDO.getSlot());
                outcomesDO.setVote(String.valueOf(answerVote));
            }
            Double myVote = predictionVotesBuilder.getAnswerVoteByCustomerId(outcomesDO.getMarketId(), outcomesDO.getSlot(), customerId);
            if (profileDTOMap.get(outcomesDO.getCustomerId()) != null && profileDTOMap.get(outcomesDO.getCustomerId()).getOpenSocial() != null) {
                outcomesDO.setHandle(profileDTOMap.get(outcomesDO.getCustomerId()).getOpenSocial().getHandle());
                outcomesDO.setNickName(profileDTOMap.get(outcomesDO.getCustomerId()).getOpenSocial().getNickName());
            } else {
                outcomesDO.setHandle("");
                outcomesDO.setNickName("");
            }
            outcomesDO.setMyVote(String.valueOf(myVote));
        }
        ProfileOpenSocialDTO profileOpenSocialDTO = profileDTOMap.get(marketDO.getCustomerId()) != null && profileDTOMap.get(marketDO.getCustomerId()).getOpenSocial() != null ? profileDTOMap.get(marketDO.getCustomerId()).getOpenSocial() : ProfileOpenSocialDTO.newBuilder().build();
        // 4. 获取参与状态（如果有用户ID）
        boolean hasParticipated = false;
        if (StringUtils.isNotBlank(customerId)) {
            hasParticipated = participantService.hasParticipated(customerId, conditionId);
        }
        Long winPoint = 0L;
        if (Ended.name().equals(marketDO.getStatus()) || Closed.name().equals(marketDO.getStatus())) {
            winPoint = winPoint(marketDO.getConditionId(), customerId);
        }


        // 5. 创建DTO
        PredictionMarketDTO predictionMarketDTO = PredictionModelConvert.toMarketDTO(marketDO, marketOutcomes, hasParticipated, profileOpenSocialDTO, winPoint);
        if (marketDO.getEndTime() < System.currentTimeMillis()) {
            predictionMarketDTO = predictionMarketDTO.toBuilder().addAllResult(getMarketVoteResult(marketDO.getConditionId()).stream().map(BigInteger::longValue).collect(Collectors.toList())).build();
            if (predictionMarketDTO.getHasParticipant()) {
                Map<String, ZeekConstants.PredictionClaimStatus> claimableStatus = queryClaimStatus(customerId, Collections.singletonList(marketDO.getConditionId()));
                if (claimableStatus != null && claimableStatus.containsKey(marketDO.getConditionId())) {
                    predictionMarketDTO = predictionMarketDTO.toBuilder().setClaimStatus(claimableStatus.get(predictionMarketDTO.getConditionId()).name()).build();
                }
            }
        }

        if (CLAIMED.name().equals(predictionMarketDTO.getClaimStatus())) {
            PredictionTransactionsDO predictionTransactionsDO = predictionTransactionsBuilder.queryClaimByCustomerId(customerId, marketDO.getConditionId());
            predictionMarketDTO = predictionMarketDTO.toBuilder().setClaimValue(predictionTransactionsDO.getTotalValue()).build();
        }
        return predictionMarketDTO;
    }

    @Override
    public PredictionMarketListDTO markets(MarketsRequest request) throws BaseException {
        PredictionMarketListDTO.Builder builder = PredictionMarketListDTO.newBuilder();
        TokenPage<PredictionMarketsDO> tokenPage = new TokenPage<PredictionMarketsDO>();
        if (request.getType().equals(MarketListType.All)) {
            tokenPage = predictionMarketsBuilder.pageSearch(request.getLimit(), request.getNextToken(), request.getStatus());
        } else if (request.getType().equals(MarketListType.Participant)) {
            List<String> marketIds = predictionParticipantsBuilder.participantsPage(request.getCustomerId(), request.getNextToken(), request.getLimit());
            List<PredictionMarketsDO> predictionMarketsDOS = predictionMarketsBuilder.getByMarketIds(marketIds);

            // 按照marketIds列表的顺序对predictionMarketsDOS进行排序
            Map<String, PredictionMarketsDO> marketMap = predictionMarketsDOS.stream()
                    .collect(Collectors.toMap(PredictionMarketsDO::getConditionId, market -> market));

            List<PredictionMarketsDO> sortedMarkets = marketIds.stream()
                    .map(marketMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            tokenPage.setRows(sortedMarkets);
        }
        // 设置下一页token
        if (tokenPage.getNextToken() != null) {
            builder.setNextToken(tokenPage.getNextToken());
        }

        // 转换市场列表
        if (tokenPage.getRows() != null && !tokenPage.getRows().isEmpty()) {
            List<PredictionMarketsDO> marketsList = tokenPage.getRows();
            // 1. 收集所有市场ID和创建者ID
            List<String> marketIds = new ArrayList<>();
            List<String> creatorIds = new ArrayList<>();
            List<String> claimStatusMarketIds = new ArrayList<>();
            // 批量查询用户参与状态
            Map<String, Boolean> participationStatus = new HashMap<>();
            String requestUserId = request.getCustomerId();
            for (PredictionMarketsDO market : marketsList) {
                marketIds.add(market.getConditionId());
                creatorIds.add(market.getCustomerId());
                if (requestUserId != null && !requestUserId.isEmpty()) {
                    String marketId = market.getConditionId();
                    if (participantService.hasParticipated(requestUserId, marketId)) {
                        participationStatus.put(marketId, true);
                        if (Closed.name().equals(market.getStatus())) {
                            claimStatusMarketIds.add(marketId);
                        }
                    } else {
                        participationStatus.put(marketId, false);
                    }
                }
            }

            // 2. 批量查询所有市场的outcomes
            List<PredictionOutcomesDO> allOutcomes = outcomesBuilder.getEffectiveRows(marketIds);

            // 3. 按市场ID分组outcomes
            Map<String, List<PredictionOutcomesDO>> outcomesByMarketId = allOutcomes.stream()
                    .collect(Collectors.groupingBy(PredictionOutcomesDO::getMarketId));

            // 4. 批量查询所有创建者的基本信息
            GetAggProfileContext getAggProfileContext = new GetAggProfileContext();
            getAggProfileContext.setIdList(creatorIds);
            getAggProfileContext.setNeedOpenSocialInfo(true);
            Map<String, ProfileDTO> creatorProfiles = profileService.getAggProfileMap(getAggProfileContext);


            Map<String, ZeekConstants.PredictionClaimStatus> claimableStatus = queryClaimStatus(request.getCustomerId(), claimStatusMarketIds);

            // 6. 构建DTO列表
            List<PredictionMarketDTO> marketDTOList = new ArrayList<>();
            for (PredictionMarketsDO market : marketsList) {
                String marketId = market.getConditionId();
                String creatorId = market.getCustomerId();
                // 获取该市场的outcomes
                List<PredictionOutcomesDO> marketOutcomes = outcomesByMarketId.getOrDefault(marketId, Collections.emptyList());
                // 按照volume转成BigDecimal做倒排序，当volume相同时按照created倒排
                marketOutcomes = marketOutcomes.stream()
                        .sorted((o1, o2) -> {
                            // 先按volume排序（倒序）
                            BigDecimal volume1 = StringUtils.isNotBlank(o1.getVolume()) ? new BigDecimal(o1.getVolume()) : BigDecimal.ZERO;
                            BigDecimal volume2 = StringUtils.isNotBlank(o2.getVolume()) ? new BigDecimal(o2.getVolume()) : BigDecimal.ZERO;
                            int volumeCompare = volume2.compareTo(volume1); // 倒序

                            // 如果volume相同，则按created排序（倒序）
                            if (volumeCompare == 0) {
                                Long created1 = o1.getCreated() != null ? o1.getCreated() : 0L;
                                Long created2 = o2.getCreated() != null ? o2.getCreated() : 0L;
                                return created2.compareTo(created1); // 倒序
                            }
                            return volumeCompare;
                        })
                        .collect(Collectors.toList());
                // 获取创建者信息
                ProfileDTO profileDTO = creatorProfiles.getOrDefault(creatorId, ProfileDTO.newBuilder().build());
                // 获取参与状态
                boolean hasParticipated = participationStatus.getOrDefault(marketId, false);
                // 创建DTO
                PredictionMarketDTO dto = PredictionModelConvert.toMarketDTO(market, marketOutcomes, hasParticipated, profileDTO.getOpenSocial() == null ? ProfileOpenSocialDTO.newBuilder().build() : profileDTO.getOpenSocial());
                if (claimableStatus.containsKey(marketId)) {
                    dto = dto.toBuilder().setClaimStatus(claimableStatus.get(marketId).name()).build();
                }
                marketDTOList.add(dto);
            }
            builder.addAllPredictionMarketDTOs(marketDTOList);
        }
        return builder.build();
    }

    @Override
    public boolean feeChanged(MarketAMMFeeChangedEventLog event) {
        PredictionMarketsDO predictionMarketsDO = predictionMarketsBuilder.getByMarketAddress(event.getMarketAddress());
        predictionMarketsDO.setFee(String.valueOf(event.getNewFee()));
        predictionMarketsDO.setCreatorFee(String.valueOf(event.getNewOwnerFee()));
        predictionMarketsDO.setProposalFee(String.valueOf(event.getNewProposalFee()));
        return predictionMarketsBuilder.updateRow(predictionMarketsDO);
    }

    @Override
    public VoteResponse vote(VoteRequest request) throws BaseException {
        VoteResponse.Builder voteResponse = VoteResponse.newBuilder();
        PredictionMarketsDO marketDO = predictionMarketsBuilder.getRowByConditionId(request.getMarketId());
        if (marketDO == null) {
            log.error("Market not found: {}", request.getMarketId());
            throw new BaseException(ZeekCoreRespondCode.MARKET_NOT_FOUND);
        }
        List<PredictionOutcomesDO> outcomesDOS = outcomesBuilder.getEffectiveRows(request.getMarketId());
        if (outcomesDOS.size() <= 1) {
            throw new BaseException(ZeekCoreRespondCode.MARKET_SLOT_NOT_ENOUGH);
        }
        Double myVote = predictionVotesBuilder.getAnswerVoteByCustomerId(request.getMarketId(), request.getSlot(), request.getCustomerId());
        if (myVote > 0) {
            throw new BaseException(ZeekCoreRespondCode.MARKET_ALREADY_EXIST);
        }
        if (request.getAmount() > predictionMarketProperties.getVote().getMax() || request.getAmount() < predictionMarketProperties.getVote().getMin()) {
            throw new BaseException(ZeekCoreRespondCode.PARAM_ILLEGAL);
        }
        String businessId = seqClient.next(VoteSeqRuleBuilder.rule());
        boolean deductPoint = questsReference.deductPoint(request.getCustomerId(), request.getAmount(), businessId, PREDICTION_VOTE_CONSUME,PREDICTION_VOTE_CONSUME.getDesc());
        if (deductPoint) {
            PredictionVotesDO predictionVotesDO = new PredictionVotesDO();
            predictionVotesDO.setId(businessId);
            predictionVotesDO.setCreated(System.currentTimeMillis());
            predictionVotesDO.setChainId(marketDO.getChainId());
            predictionVotesDO.setSlot(request.getSlot());
            predictionVotesDO.setValue(request.getAmount());
            predictionVotesDO.setDirection(SEND.getCode());
            predictionVotesDO.setModified(System.currentTimeMillis());
            predictionVotesDO.setMarketId(request.getMarketId());
            predictionVotesDO.setCustomerId(request.getCustomerId());
            predictionVotesBuilder.putRow(predictionVotesDO);
            participantService.participate(request.getCustomerId(), request.getMarketId(), ZeekConstants.PredictionAction.VOTE.name());
            voteResponse.setSuccess(true)
                    .setSlot(request.getSlot())
                    .setAmount(request.getAmount());
            predictionMarketsBuilder.updateAndIncRow(marketDO, null, List.of(new Column("vote", ColumnValue.fromLong(request.getAmount()))), null, null);

        } else {
            voteResponse.setSuccess(false);
        }
        return voteResponse.build();
    }

    @Override
    public boolean updateFunding(String conditionId, BigInteger funding, BigInteger timestamp) {
        PredictionMarketsDO marketsDO = predictionMarketsBuilder.getRowByConditionId(conditionId);
        if (marketsDO == null) {
            log.warn("market not exist, conditionId: {}", conditionId);
            return false;
        }

        if (marketsDO.getEventTime() != null && marketsDO.getEventTime() >= timestamp.longValue()) {
            log.warn("market funding already updated, conditionId: {}, eventTime: {}, timestamp: {}", conditionId, marketsDO.getEventTime(), timestamp);
            return true;
        }
        log.info("market funding update, conditionId: {}, oldFunding: {}, eventTime: {}, funding: {}, timestamp: {}",
                conditionId, marketsDO.getFunding(), marketsDO.getEventTime(), funding, timestamp);

        return predictionMarketsBuilder.updateFunding(marketsDO, funding.toString(), timestamp.longValue());
    }

    @Override
    public boolean expiredPrediction(MarketActionContext context) throws BaseException {
        // 驱动prediction到ended状态
        log.info("expired prediction context:{}", JSON.toJSONString(context));
        return stateMachine.action(MarketStatus.Running, ZEEK_EXPIRED_MARKET, context);
    }

    @Override
    public List<BigInteger> getMarketVoteResult(String marketId) throws BaseException {
        // 参数校验
        if (StringUtils.isBlank(marketId)) {
            throw new BaseException(ZeekCoreRespondCode.PARAM_ILLEGAL);
        }
        // 检查市场是否存在
        PredictionMarketsDO marketDO = predictionMarketsBuilder.getRowByConditionId(marketId);
        if (marketDO == null) {
            log.error("Market not found: {}", marketId);
            throw new BaseException(ZeekCoreRespondCode.MARKET_NOT_FOUND);
        }
        if (!Ended.name().equals(marketDO.getStatus()) && !Closed.name().equals(marketDO.getStatus())) {
            log.error("Market status not ended: {}", marketId);
            throw new BaseException(ZeekCoreRespondCode.MARKET_NOT_ENDED);
        }

        // 先尝试从缓存获取结果
        List<BigInteger> cachedResult = marketVoteResultService.getCachedVoteResult(marketId);
        if (cachedResult != null) {
            log.info("Found cached vote result for market: {}, result: {}", marketId, cachedResult);
            return cachedResult;
        }

        // 如果缓存中没有，则重新计算
        List<PredictionOutcomesDO> outcomesDOS = outcomesBuilder.getEffectiveRows(marketId);
        int outcomeCount = outcomesDOS.size();

        if (outcomeCount == 0) {
            log.warn("No outcomes found for market: {}", marketId);
            return Collections.emptyList();
        }

        // 计算并缓存结果
        List<BigInteger> result = marketVoteResultService.calculateAndCacheVoteResult(marketId, outcomeCount);

        log.info("Calculated vote result for market: {}, result: {}", marketId, result);
        return result;
    }


    @Override
    public CloseMarketResponse closeMarket(CloseMarketRequest request) throws BaseException, IOException, IllegalAccessException {
        PayoutDataTypedMessage message = new PayoutDataTypedMessage();
        PredictionMarketsDO predictionMarketsDO = predictionMarketsBuilder.getRowByConditionId(request.getMarketId());
        // 获取投票结果
        List<BigInteger> voteResults = getMarketVoteResult(request.getMarketId());
        PredictionCloseSignatureUtils.PayoutsData data = new PredictionCloseSignatureUtils.PayoutsData();
        data.setConditionId(new Bytes32(Numeric.hexStringToByteArray(request.getMarketId())));
        data.setPayouts(voteResults);
        message.setConditionId(data.getConditionId());
        message.setPayoutIds(data.getPayouts());
        String signature = getSigner(ZeekConstants.QUEST_VERIFY + ZeekConstants.SIGNER)
                .signMessage(message.getMessageBytes(predictionMarketsDO.getMarketAddress(),
                        predictionMarketProperties.getMarketContractName(),
                        predictionMarketProperties.getMarketContractVersion(),
                        ChainIdUtil.getChainId().intValue(),
                        PredictionCloseSignatureUtils.encodePayoutsData(data)));

        // 构建响应
        CloseMarketResponse.Builder responseBuilder = CloseMarketResponse.newBuilder()
                .setMarketId(request.getMarketId())
                .setSignature(signature);

        // 将投票结果转换为字符串列表添加到payouts中
        for (BigInteger result : voteResults) {
            responseBuilder.addPayouts(result.toString());
        }

        log.info("Market closed successfully: {}, vote results: {}", request.getMarketId(), voteResults);

        return responseBuilder.build();
    }

    /**
     * 生成积分发放缓存key
     * @param marketId 市场ID
     * @param customerId 用户ID
     * @param slot 槽位
     * @return 缓存key
     */
    private String getPointSettleCacheKey(String marketId, String customerId, Integer slot) {
        return String.format("prediction:point:settle:%s:%s:%d", marketId, customerId, slot);
    }

    public ISigner getSigner(String type) {
        return signers.get(type);
    }

    protected Long winPoint(String marketId, String customerId) throws BaseException {
        Double addPoint = predictionVotesBuilder.getMarketSettle(marketId, customerId);
        if (addPoint != null && addPoint > 0) {return addPoint.longValue();}

        List<BigInteger> voteResults = getMarketVoteResult(marketId);
        List<Integer> winnerSlots = new ArrayList<>();
        List<Integer> losingSlots = new ArrayList<>();
        for (int i = 0; i < voteResults.size(); i++) {
            if (BigInteger.ONE.equals(voteResults.get(i))) {
                winnerSlots.add(i);
            } else {
                losingSlots.add(i);
            }
        }
        Double userVote = predictionVotesBuilder.getAnswerVoteByCustomerId(marketId, winnerSlots, customerId);
        if (userVote == null || userVote <= 0) {return 0L;}

        Double winPoint = predictionVotesBuilder.getAnswerVote(marketId, winnerSlots);
        Double losePoint = predictionVotesBuilder.getAnswerVote(marketId, losingSlots);

        BigDecimal userVoteValue = new BigDecimal(userVote);
        BigDecimal winPointDecimal = new BigDecimal(winPoint);
        BigDecimal losePointDecimal = new BigDecimal(losePoint);

        // amount = (votesDO.getValue()/winPoint) * losePoint + votesDO.getValue()
        BigDecimal amount = userVoteValue.divide(winPointDecimal, 10, BigDecimal.ROUND_HALF_UP)
                .multiply(losePointDecimal)
                .add(userVoteValue);

        // 向下取整转为整数
        Long finalAmount = amount.longValue();
        return finalAmount;
    }


    @Override
    public boolean settleMarket(String marketId) throws BaseException {
        // 1. 获取投票结果
        List<BigInteger> voteResults = getMarketVoteResult(marketId);
        if (voteResults == null || voteResults.isEmpty()) {
            log.warn("settleMarket: No vote results for marketId: {}", marketId);
            return false;
        }
        // 2. 找到所有获胜slot 与失败slot
        List<Integer> winnerSlots = new ArrayList<>();
        List<Integer> losingSlots = new ArrayList<>();
        for (int i = 0; i < voteResults.size(); i++) {
            if (BigInteger.ONE.equals(voteResults.get(i))) {
                winnerSlots.add(i);
            } else {
                losingSlots.add(i);
            }
        }
        if (winnerSlots.isEmpty()) {
            log.warn("settleMarket: No winner slot found for marketId: {}", marketId);
            return false;
        }
        // 3. 分页遍历获胜用户
        int limit = 100;
        int offset = 0;
        Double allPoint = predictionVotesBuilder.getMarketVote(marketId);
        Double winPoint = predictionVotesBuilder.getAnswerVote(marketId, winnerSlots);
        Double losePoint = predictionVotesBuilder.getAnswerVote(marketId, losingSlots);
        if (winPoint + losePoint != allPoint) {
            log.error("settleMarket: Abnormal voting score, please check: {} allPoint: {} winPoint: {} losePoint: {}", marketId, allPoint, winPoint, losePoint);
            return false;
        }
        for (Integer slot : winnerSlots) {
            while (true) {
                List<PredictionVotesDO> page = predictionVotesBuilder.getWinerPage(marketId, slot, String.valueOf(offset), limit);
                if (page == null || page.isEmpty()) {
                    break;
                }
                for (PredictionVotesDO votesDO : page) {
                    try {
                        // 先从Redis缓存查询是否已经发放过积分
                        String cacheKey = getPointSettleCacheKey(votesDO.getMarketId(), votesDO.getCustomerId(), slot);
                        String cachedPoint = redisTemplate.opsForValue().get(cacheKey);
                        if (StringUtils.isNotBlank(cachedPoint)) {
                            log.info("settleMarket: point already added from cache customer Id {} slot: {} point : {}", votesDO.getCustomerId(), slot, cachedPoint);
                            continue;
                        }
                        
                        // 判断是否加过分了
                        Double addPoint = predictionVotesBuilder.getMarketSettle(votesDO.getMarketId(), votesDO.getCustomerId(), slot);
                        if (addPoint != null && addPoint > 0) {
                            log.info("settleMarket: point already added customer Id {} slot: {} point : {}", votesDO.getCustomerId(), slot, addPoint);
                            continue;
                        }
                        // 计算分数
                        BigDecimal userVoteValue = new BigDecimal(votesDO.getSumValue());
                        BigDecimal winPointDecimal = new BigDecimal(winPoint);
                        BigDecimal losePointDecimal = new BigDecimal(losePoint);

                        // amount = (votesDO.getValue()/winPoint) * losePoint + votesDO.getValue()
                        BigDecimal amount = userVoteValue.divide(winPointDecimal, 10, BigDecimal.ROUND_HALF_UP)
                                .multiply(losePointDecimal)
                                .add(userVoteValue);

                        // 向下取整转为整数
                        Long finalAmount = amount.longValue();
                        // 加分处理
                        String businessId = seqClient.next(VoteSeqRuleBuilder.rule());
                        boolean result = questsReference.addPoint(votesDO.getCustomerId(), finalAmount, businessId, PREDICTION_VOTE_WIN, PREDICTION_VOTE_WIN.getDesc());
                        if (result) {
                            PredictionVotesDO predictionVotesDO = new PredictionVotesDO();
                            predictionVotesDO.setId(businessId);
                            predictionVotesDO.setCreated(System.currentTimeMillis());
                            predictionVotesDO.setChainId(votesDO.getChainId());
                            predictionVotesDO.setValue(finalAmount);
                            predictionVotesDO.setModified(System.currentTimeMillis());
                            predictionVotesDO.setCustomerId(votesDO.getCustomerId());
                            predictionVotesDO.setMarketId(votesDO.getMarketId());
                            predictionVotesDO.setSlot(slot);
                            predictionVotesDO.setDirection(RECEIVE.getCode());
                            predictionVotesBuilder.putRow(predictionVotesDO);
                            // 将积分发放结果缓存到Redis，缓存3分钟
                            redisTemplate.opsForValue().set(cacheKey, String.valueOf(finalAmount), 3, TimeUnit.MINUTES);
                            log.info("settleMarket: point added success marketId:{} slot:{} customer Id:{} allPoint:{}, winPoint:{} losePoint:{} votePoint:{} addPoint : {}", marketId, slot, votesDO.getCustomerId(), allPoint, winPointDecimal, losePointDecimal, userVoteValue, finalAmount);
                        } else {
                            log.info("settleMarket: point added failed marketId:{} slot:{} customer Id:{} allPoint:{}, winPoint:{} losePoint:{} votePoint:{} addPoint : {}", marketId, slot, votesDO.getCustomerId(), allPoint, winPointDecimal, losePointDecimal, userVoteValue, finalAmount);
                        }
                        // 记录分数
                    } catch (Exception e) {
                        log.error("settle vote result failed customerId {} marketId {} slot {}", votesDO.getCustomerId(), votesDO.getMarketId(), slot, e);
                    }
                }
                if (page.size() < limit) {
                    break;
                }
                offset += limit;
            }
        }
        return true;
    }

    @Override
    public Map<String, ZeekConstants.PredictionClaimStatus> queryClaimStatus(String customerId, List<String> marketIds) throws BaseException {
        Map<String, ZeekConstants.PredictionClaimStatus> result = Maps.newHashMap();
        if (StringUtils.isBlank(customerId) || CollectionUtils.isEmpty(marketIds)) {
            return result;
        }

        // 查询用户已经 claim 的市场，如果已经 claim 直接返回 PredictionClaimStatus.CLAIMED
        List<PredictionTransactionsDO> allTransactionsDOS = predictionTransactionsBuilder.queryByCustomerId(customerId, marketIds);

        for (String marketId : marketIds) {
            // 过滤得到  marketId 的数据
            List<PredictionTransactionsDO> marketTransactionsDOS = allTransactionsDOS.stream().filter(t -> t.getMarketId().equals(marketId)).collect(Collectors.toList());

            // 如果存在一条 action 为 CLAIM，则返回 PredictionClaimStatus.CLAIMED
            if (marketTransactionsDOS.stream().anyMatch(t -> t.getAction() == ZeekConstants.PredictionAction.CLAIM.getCode())) {
                result.put(marketId, CLAIMED);
                continue;
            }

            // 如果存在一条 action 为 POST，则返回 PredictionClaimStatus.CLAIMABLE
            if (marketTransactionsDOS.stream().anyMatch(t -> t.getAction() == ZeekConstants.PredictionAction.POST.getCode())) {
                result.put(marketId, ZeekConstants.PredictionClaimStatus.CLAIMABLE);
                continue;
            }

            // 如果用户 OUTCOME 过，且该条 OUTCOME 存在 TRADE 的数据，则返回 PredictionClaimStatus.CLAIMABLE
            PredictionTransactionsDO outcomeTransaction = marketTransactionsDOS.stream().filter(t -> t.getAction() == ZeekConstants.PredictionAction.OUTCOME.getCode()).findFirst().orElse(null);
            if (outcomeTransaction != null) {
                if (marketTransactionsDOS.stream().anyMatch(t -> t.getSlot().equals(outcomeTransaction.getSlot()) && t.getAction() == ZeekConstants.PredictionAction.TRADE.getCode())) {
                    result.put(marketId, ZeekConstants.PredictionClaimStatus.CLAIMABLE);
                    continue;
                }
            }

            // 获取该 market 下所有的 outcomes
            List<PredictionOutcomesDO> outcomes = outcomesBuilder.getEffectiveRows(marketId);
            // 如果 outcomes 中存在 customerId 的数据，且 status = PREFECT，则返回 PredictionClaimStatus.CLAIMABLE
            if (outcomes.stream().anyMatch(o -> o.getCustomerId().equals(customerId) && o.getStatus() == ZeekConstants.OutcomeStatus.PERFECT.getCode())) {
                result.put(marketId, ZeekConstants.PredictionClaimStatus.CLAIMABLE);
                continue;
            }

            // 获取用户当前市场的持仓，如果存在 outcome = PEREFECT，对应 outcome 的持仓还大于 0 ，则返回 PredictionClaimStatus.CLAIMABLE
            List<Integer> perfectSlots = outcomes.stream().filter(o -> o.getStatus() == ZeekConstants.OutcomeStatus.PERFECT.getCode()).map(PredictionOutcomesDO::getSlot).collect(Collectors.toList());
            List<PredictionPositionsDO> positionsDOS = positionsBuilder.queryByMarketId(customerId, marketId);
            if (CollectionUtils.isNotEmpty(positionsDOS)) {
                if (positionsDOS.stream().anyMatch(p -> perfectSlots.contains(p.getSlot()) && new BigInteger(p.getValue()).compareTo(BigInteger.ZERO) > 0)) {
                    result.put(marketId, ZeekConstants.PredictionClaimStatus.CLAIMABLE);
                }
            }

            // 如果上边都不满足，则返回 PredictionClaimStatus.NONE
            result.put(marketId, ZeekConstants.PredictionClaimStatus.NONE);
        }

        log.info("queryClaimStatus: customerId:{}, result:{}", customerId, JSON.toJSONString(result));
        return result;
    }


}
