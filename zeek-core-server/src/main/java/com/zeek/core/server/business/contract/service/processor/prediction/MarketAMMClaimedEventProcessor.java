package com.zeek.core.server.business.contract.service.processor.prediction;

import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.kikitrade.framework.observability.tracing.annotation.TracingSpan;
import com.kikitrade.framework.observability.tracing.constant.TracingBusiness;
import com.zeek.core.common.exception.BaseException;
import com.zeek.core.contract.common.EventTypeConstants;
import com.zeek.core.contract.model.event.prediction.MarketAMMClaimedEventLog;
import com.zeek.core.server.business.contract.service.EventLogProcessor;
import com.zeek.core.server.business.prediction.service.PredictionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @author: xiang.bai
 * @description:
 * @create: 2024-08-01
 **/
@Slf4j
@Component(EventTypeConstants.MARKET_AMM_CLAIMED_EVENT)
public class MarketAMMClaimedEventProcessor implements EventLogProcessor<MarketAMMClaimedEventLog> {

    @Resource
    PredictionService predictionService;

    @Override
    public MarketAMMClaimedEventLog parseEventLog(String message) {
        return JSON.parseObject(message, MarketAMMClaimedEventLog.class);
    }

    @Override
    @TracingSpan(business = TracingBusiness.none)
    public boolean process(MarketAMMClaimedEventLog event) {
        try {
            return predictionService.handleClaimOnChain(event);
        } catch (BaseException e) {
            log.error("handleClaimOnChain error: ", e);
            return false;
        }
    }

}