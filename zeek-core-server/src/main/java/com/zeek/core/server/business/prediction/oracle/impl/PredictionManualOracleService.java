package com.zeek.core.server.business.prediction.oracle.impl;

import com.zeek.core.api.respond.ZeekCoreRespondCode;
import com.zeek.core.common.exception.BaseException;
import com.zeek.core.dal.ots.builder.PredictionOutcomesBuilder;
import com.zeek.core.dal.ots.model.PredictionOutcomesDO;
import com.zeek.core.server.business.prediction.oracle.PredictionOracleService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: lizhi<PERSON>
 * @date: 2025/6/9 20:00
 */
@Slf4j
@Component
public class PredictionManualOracleService implements PredictionOracleService {

    @Resource
    PredictionOutcomesBuilder outcomesBuilder;

    @Override
    public List<BigInteger> getMarketResult(String marketId, int winSlot) throws BaseException {
        List<PredictionOutcomesDO> outcomesDOS = outcomesBuilder.getEffectiveRows(marketId);
        if (CollectionUtils.isEmpty(outcomesDOS)) {
            return new ArrayList<>();
        }

        int outcomeCount = outcomesDOS.size();
        if (outcomeCount - 1 < winSlot) {
            log.error("Invalid slot: marketId: {}, slot: {}, outcomeCount: {}", marketId, winSlot, outcomeCount);
            throw new BaseException(ZeekCoreRespondCode.OUTCOME_NOT_ALLOWED);
        }

        // 构建一个 List<BigInteger>，默认值是 0，当 winSlot 的位置设置为 1
        List<BigInteger> result = new ArrayList<>(outcomeCount);
        for (int i = 0; i < outcomeCount; i++) {
            result.add(BigInteger.ZERO);
        }
        result.set(winSlot, BigInteger.ONE);
        return result;
    }
}
