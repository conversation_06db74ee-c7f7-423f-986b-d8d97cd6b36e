package com.zeek.core.server.business.prediction.service;

import com.zeek.core.common.exception.BaseException;

import java.math.BigInteger;
import java.util.List;

/**
 * 市场投票结果服务
 *
 * @author: AI Assistant
 * @date: 2025/1/27
 */
public interface PredictionVoteService {

    /**
     * 从缓存获取市场投票结果
     *
     * @param marketId 市场ID
     * @return 投票结果数组，1表示获胜，0表示失败；如果缓存中没有则返回null
     */
    List<BigInteger> getCachedVoteResult(String marketId);

    /**
     * 计算并缓存市场投票结果
     *
     * @param marketId 市场ID
     * @param outcomeCount 结果选项数量
     * @return 投票结果数组，1表示获胜，0表示失败
     * @throws BaseException 异常信息
     */
    List<BigInteger> calculateAndCacheVoteResult(String marketId, int outcomeCount) throws BaseException;

    /**
     * 获取市场投票结果（包含市场状态验证和缓存逻辑）
     *
     * @param marketId 市场ID
     * @return 投票结果数组，1表示获胜，0表示失败
     * @throws BaseException 异常信息
     */
    List<BigInteger> getMarketVoteResult(String marketId) throws BaseException;

    /**
     * 清除市场投票结果缓存
     *
     * @param marketId 市场ID
     */
    void clearVoteResultCache(String marketId);
}
