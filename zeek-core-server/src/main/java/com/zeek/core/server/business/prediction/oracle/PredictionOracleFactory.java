package com.zeek.core.server.business.prediction.oracle;

import com.zeek.core.common.constants.ZeekConstants;
import com.zeek.core.server.business.prediction.oracle.impl.PredictionManualOracleService;
import com.zeek.core.server.business.prediction.oracle.impl.PredictionVoteOracleService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @author: l<PERSON><PERSON><PERSON>
 * @date: 2025/6/9 19:59
 */
@Slf4j
@Component
public class PredictionOracleFactory {

    @Resource
    private PredictionVoteOracleService voteOracleService;
    @Resource
    private PredictionManualOracleService manualOracleService;

    public PredictionOracleService getOracleService(ZeekConstants.PredictionOracleType type) {
        switch (type) {
            case Vote:
                return voteOracleService;
            case Manual:
                return manualOracleService;
            default:
                throw new RuntimeException("oracle type not found: " + type);
        }
    }

}
