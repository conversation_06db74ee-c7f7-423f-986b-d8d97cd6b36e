package com.zeek.core.server.business.reference.executor.impl;

import cn.hutool.core.util.NumberUtil;
import co.evg.scaffold.innerEvent.common.annotation.ExecutorTopics;
import co.evg.scaffold.innerEvent.common.constants.InnerEventConstants;
import co.evg.scaffold.innerEvent.service.executor.impl.AbstractInnerExecutor;
import com.alibaba.fastjson.JSONObject;
import com.kikitrade.framework.common.util.ChainIdUtil;
import com.zeek.core.common.constants.CoreInnerEventConstants;
import com.zeek.core.common.constants.ZeekConstants;
import com.zeek.core.contract.model.event.prediction.MarketAMMClaimedEventLog;
import com.zeek.core.dal.ots.builder.PredictionTransactionsBuilder;
import com.zeek.core.dal.ots.model.PredictionMarketsDO;
import com.zeek.core.dal.ots.model.PredictionTransactionsDO;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: joseph
 * @date: 2024/8/28 12:18
 */
@Component
@Slf4j
@ExecutorTopics(value = {CoreInnerEventConstants.MARKET_CLAIM})
public class PredictionClaimedInnerExecutor extends AbstractInnerExecutor {

    @Resource
    PredictionTransactionsBuilder transactionsBuilder;

    @SneakyThrows
    @Override
    public InnerEventConstants.DeliveryStatus doExecute(JSONObject input) {
        log.info("MARKET_CLAIM params is {} ", input.toJSONString());
        String customerId = input.getString(CoreInnerEventConstants.CUSTOMER_ID);
        PredictionMarketsDO marketsDO = input.getObject(CoreInnerEventConstants.MARKET, PredictionMarketsDO.class);
        MarketAMMClaimedEventLog eventLog = input.getObject(CoreInnerEventConstants.EVENT, MarketAMMClaimedEventLog.class);

        PredictionTransactionsDO predictionTransactionsDO = new PredictionTransactionsDO();
        predictionTransactionsDO.setMarketId(marketsDO.getConditionId());
        predictionTransactionsDO.setCustomerId(customerId);
        predictionTransactionsDO.setAction(ZeekConstants.PredictionAction.CLAIM.getCode());
        predictionTransactionsDO.setBizId(StringUtils.join(List.of(ZeekConstants.PredictionAction.CLAIM.name(), eventLog.getLog().getTransactionHash()), "_"));
        predictionTransactionsDO.setChainId(String.valueOf(ChainIdUtil.getChainId()));
        predictionTransactionsDO.setDirection(ZeekConstants.WalletTransactionDirection.RECEIVE.getCode());
        predictionTransactionsDO.setToken(marketsDO.getToken());
        predictionTransactionsDO.setValue(eventLog.getFundingAmount().toString());
        predictionTransactionsDO.setFee(eventLog.getFeeAmount().toString());
        predictionTransactionsDO.setMarginValue(eventLog.getMarginAmount().toString());
        // 将 fundingAmount + marginAmount + feeAmount = totalValue
        BigDecimal totalValue = NumberUtil.add(eventLog.getFundingAmount(), eventLog.getMarginAmount(), eventLog.getFeeAmount());
        predictionTransactionsDO.setTotalValue(totalValue.toString());
        predictionTransactionsDO.setTimestamp(System.currentTimeMillis());
        predictionTransactionsDO.setTransactionHash(eventLog.getLog().getTransactionHash());
        predictionTransactionsDO.setCreated(System.currentTimeMillis());
        predictionTransactionsDO.setModified(System.currentTimeMillis());

        transactionsBuilder.putPow(predictionTransactionsDO);
        return InnerEventConstants.DeliveryStatus.SUCCESS;
    }

}
