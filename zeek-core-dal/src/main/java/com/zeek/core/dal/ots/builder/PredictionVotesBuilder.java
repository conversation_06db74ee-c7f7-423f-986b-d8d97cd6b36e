package com.zeek.core.dal.ots.builder;

import com.alicloud.openservices.tablestore.model.search.SearchQuery;
import com.alicloud.openservices.tablestore.model.search.agg.CountAggregation;
import com.alicloud.openservices.tablestore.model.search.agg.SumAggregation;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.google.common.collect.Lists;
import com.kikitrade.framework.common.util.ChainIdUtil;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.sql.SQL;
import com.zeek.core.common.constants.ZeekConstants;
import com.zeek.core.dal.ots.model.PredictionParticipantsDO;
import com.zeek.core.dal.ots.model.PredictionVotesDO;
import com.zeek.core.dal.ots.model.WishActionDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.zeek.core.common.constants.ZeekConstants.WalletTransactionDirection.RECEIVE;
import static com.zeek.core.common.constants.ZeekConstants.WalletTransactionDirection.SEND;


/**
 * <AUTHOR>
 * @description
 * @param[1] null
 * @date 2025/5/15 17:29
 **/
@Repository
public class PredictionVotesBuilder extends WideColumnStoreBuilder<PredictionVotesDO> {
    @PostConstruct
    public void init() {
        init(PredictionVotesDO.class);
    }


    public Double getMarketVote(String marketId) {
        BoolQuery.Builder builder = QueryBuilders.bool();
        builder.filter(QueryBuilders.term("chain_id", ChainIdUtil.getChainId()));
        builder.filter(QueryBuilders.term("market_id", marketId));
        builder.filter(QueryBuilders.term("direction", SEND.getCode()));

        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setQuery(builder.build());
        searchQuery.setLimit(1);
        searchQuery.setGetTotalCount(true);
        SumAggregation sumAggregation = new SumAggregation();
        sumAggregation.setFieldName("value");
        sumAggregation.setAggName("count");
        searchQuery.setAggregationList(Collections.singletonList(sumAggregation));
        return searchSum(searchQuery, PredictionVotesDO.SEARCH_INDEX_PREDICTION_VOTES, "count");
    }

    public Double getAnswerVote(String marketId, Integer slot) {
        BoolQuery.Builder builder = QueryBuilders.bool();
        builder.filter(QueryBuilders.term("chain_id", ChainIdUtil.getChainId()));
        builder.filter(QueryBuilders.term("market_id", marketId));
        builder.filter(QueryBuilders.term("slot", slot));
        builder.filter(QueryBuilders.term("direction", SEND.getCode()));

        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setQuery(builder.build());
        searchQuery.setLimit(1);
        searchQuery.setGetTotalCount(true);
        SumAggregation sumAggregation = new SumAggregation();
        sumAggregation.setFieldName("value");
        sumAggregation.setAggName("count");
        searchQuery.setAggregationList(Collections.singletonList(sumAggregation));
        return searchSum(searchQuery, PredictionVotesDO.SEARCH_INDEX_PREDICTION_VOTES, "count");
    }

    public Double getAnswerVote(String marketId, List<Integer> slot) {
        BoolQuery.Builder builder = QueryBuilders.bool();
        builder.filter(QueryBuilders.term("chain_id", ChainIdUtil.getChainId()));
        builder.filter(QueryBuilders.term("market_id", marketId));
        builder.filter(QueryBuilders.terms("slot").terms(slot.toArray()));
        builder.filter(QueryBuilders.term("direction", SEND.getCode()));

        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setQuery(builder.build());
        searchQuery.setLimit(1);
        searchQuery.setGetTotalCount(true);
        SumAggregation sumAggregation = new SumAggregation();
        sumAggregation.setFieldName("value");
        sumAggregation.setAggName("count");
        searchQuery.setAggregationList(Collections.singletonList(sumAggregation));
        return searchSum(searchQuery, PredictionVotesDO.SEARCH_INDEX_PREDICTION_VOTES, "count");
    }


    public Double getAnswerVoteByCustomerId(String marketId, Integer slot, String customerId) {
        BoolQuery.Builder builder = QueryBuilders.bool();
        builder.filter(QueryBuilders.term("chain_id", ChainIdUtil.getChainId()));
        builder.filter(QueryBuilders.term("market_id", marketId));
        builder.filter(QueryBuilders.term("slot", slot));
        builder.filter(QueryBuilders.term("customer_id", customerId));
        builder.filter(QueryBuilders.term("direction", SEND.getCode()));


        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setQuery(builder.build());
        searchQuery.setLimit(1);
        searchQuery.setGetTotalCount(true);
        SumAggregation sumAggregation = new SumAggregation();
        sumAggregation.setFieldName("value");
        sumAggregation.setAggName("count");
        searchQuery.setAggregationList(Collections.singletonList(sumAggregation));
        return searchSum(searchQuery, PredictionVotesDO.SEARCH_INDEX_PREDICTION_VOTES, "count");
    }

    public Double getAnswerVoteByCustomerId(String marketId, List<Integer> slot, String customerId) {
        BoolQuery.Builder builder = QueryBuilders.bool();
        builder.filter(QueryBuilders.term("chain_id", ChainIdUtil.getChainId()));
        builder.filter(QueryBuilders.term("market_id", marketId));
        builder.filter(QueryBuilders.terms("slot").terms(slot.toArray()));
        builder.filter(QueryBuilders.term("customer_id", customerId));
        builder.filter(QueryBuilders.term("direction", SEND.getCode()));


        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setQuery(builder.build());
        searchQuery.setLimit(1);
        searchQuery.setGetTotalCount(true);
        SumAggregation sumAggregation = new SumAggregation();
        sumAggregation.setFieldName("value");
        sumAggregation.setAggName("count");
        searchQuery.setAggregationList(Collections.singletonList(sumAggregation));
        return searchSum(searchQuery, PredictionVotesDO.SEARCH_INDEX_PREDICTION_VOTES, "count");
    }


    public List<PredictionVotesDO> getWinerPage(String marketId, List<Integer> slots, String offset, Integer limit) {
        if (StringUtils.isBlank(offset)) {
            offset = "0";
        }
        String slotCondition = "1=1";
        if (slots != null && !slots.isEmpty()) {
            slotCondition = "slot in (" + slots.stream().map(String::valueOf).collect(java.util.stream.Collectors.joining(",")) + ")";
        }
        SQL sql = new SQL()
                .SELECT("customer_id,sum(value) as value")
                .FROM(String.format("%s", PredictionVotesDO.TABLE_NAME))
                .WHERE(String.format(
                        "market_id='%s' and %s and direction = '-1' ", marketId, slotCondition))
                .GROUP_BY("customer_id")
                .ORDER_BY("sum(value) desc")
                .LIMIT(String.format("%s,%s", offset, limit));
        return sqlQuery(sql);
    }

    public List<PredictionVotesDO> getWinerPage(String marketId, Integer slot, String offset, Integer limit) {
        if (StringUtils.isBlank(offset)) {
            offset = "0";
        }
        SQL sql = new SQL()
                .SELECT("customer_id,sum(value) as sumValue, max(market_id) as market_id, max(slot) as slot, max(chain_id) as chain_id")
                .FROM(String.format("%s", PredictionVotesDO.TABLE_NAME))
                .WHERE(String.format(
                        "market_id='%s' and slot = '%s' and direction = '-1' ", marketId, slot))
                .GROUP_BY("customer_id")
                .ORDER_BY("sum(value) desc")
                .LIMIT(String.format("%s,%s", offset, limit));
        return sqlQuery(sql);
    }

    public Double getMarketSettle(String marketId, String customerId, Integer slot) {

        BoolQuery.Builder builder = QueryBuilders.bool();
        builder.filter(QueryBuilders.term("chain_id", ChainIdUtil.getChainId()));
        builder.filter(QueryBuilders.term("market_id", marketId));
        builder.filter(QueryBuilders.term("slot", slot));
        builder.filter(QueryBuilders.term("customer_id", customerId));
        builder.filter(QueryBuilders.term("direction", RECEIVE.getCode()));

        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setQuery(builder.build());
        searchQuery.setLimit(1);
        searchQuery.setGetTotalCount(true);
        SumAggregation sumAggregation = new SumAggregation();
        sumAggregation.setFieldName("value");
        sumAggregation.setAggName("count");
        searchQuery.setAggregationList(Collections.singletonList(sumAggregation));
        return searchSum(searchQuery, PredictionVotesDO.SEARCH_INDEX_PREDICTION_VOTES, "count");
    }

    public Double getMarketSettle(String marketId, String customerId) {

        BoolQuery.Builder builder = QueryBuilders.bool();
        builder.filter(QueryBuilders.term("chain_id", ChainIdUtil.getChainId()));
        builder.filter(QueryBuilders.term("market_id", marketId));
        builder.filter(QueryBuilders.term("customer_id", customerId));
        builder.filter(QueryBuilders.term("direction", RECEIVE.getCode()));

        SearchQuery searchQuery = new SearchQuery();
        searchQuery.setQuery(builder.build());
        searchQuery.setLimit(1);
        searchQuery.setGetTotalCount(true);
        SumAggregation sumAggregation = new SumAggregation();
        sumAggregation.setFieldName("value");
        sumAggregation.setAggName("count");
        searchQuery.setAggregationList(Collections.singletonList(sumAggregation));
        return searchSum(searchQuery, PredictionVotesDO.SEARCH_INDEX_PREDICTION_VOTES, "count");
    }
}
