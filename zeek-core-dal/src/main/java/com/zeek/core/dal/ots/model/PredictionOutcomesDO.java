package com.zeek.core.dal.ots.model;

import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Transient;
import com.zeek.core.common.constants.ZeekConstants;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * @author: lizhifeng
 * @date: 2025/5/15 17:48
 */
@Data
public class PredictionOutcomesDO {

    public static final String TABLE_NAME = "prediction_outcomes";
    public static final String SEARCH_INDEX_PREDICTION_OUTCOMES = "search_idx_prediction_outcomes";
    public static final String SEARCH_INDEX_PREDICTION_OUTCOMES_V2 = "search_idx_prediction_outcomes_v2";

    @PartitionKey(name = "market_id", value = 0)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_OUTCOMES_V2, column = "market_id", fieldType = FieldType.KEYWORD)
    private String marketId;

    @PartitionKey(name = "customer_id", value = 1)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_OUTCOMES_V2, column = "customer_id", fieldType = FieldType.KEYWORD)
    private String customerId;

    @PartitionKey(name = "chain_id", value = 2)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_OUTCOMES_V2, column = "chain_id", fieldType = FieldType.KEYWORD)
    private String chainId;

    @Column(name = "position_id",isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_OUTCOMES_V2, column = "position_id", fieldType = FieldType.KEYWORD)
    private String positionId;

    @Column(name = "slot",isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_OUTCOMES_V2, column = "slot", fieldType = FieldType.LONG)
    private Integer slot;

    @Column(name = "chance",isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_OUTCOMES_V2, column = "chance", fieldType = FieldType.DOUBLE)
    private Double chance;

    @Column(name = "content",isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_OUTCOMES_V2, column = "content", fieldType = FieldType.KEYWORD)
    private String content;

    @Column(name = "token",isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_OUTCOMES_V2, column = "token", fieldType = FieldType.KEYWORD)
    private String token;

    @Column(name = "value",isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_OUTCOMES_V2, column = "value", fieldType = FieldType.KEYWORD)
    private String value;

    /**
     * @see ZeekConstants.OutcomeStatus
     */
    @Column(name = "status",isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_OUTCOMES_V2, column = "status", fieldType = FieldType.LONG)
    private Integer status;

    @Column(name = "volume",isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_OUTCOMES_V2, column = "volume", fieldType = FieldType.KEYWORD)
    private String volume;

    @Column(name = "volume_usd",isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_OUTCOMES_V2, column = "volume_usd", fieldType = FieldType.DOUBLE)
    private Double volumeUsd;

    /**
     * 该回答的总仓位（不包含虚拟份额）
     */
    @Column(name = "total_position",isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_OUTCOMES_V2, column = "total_position", fieldType = FieldType.KEYWORD)
    private String totalPosition;

    @Column(name = "event_time",isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_OUTCOMES_V2, column = "event_time", fieldType = FieldType.LONG)
    private Long eventTime;

    @Column(name = "version",isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_OUTCOMES_V2, column = "version", fieldType = FieldType.LONG)
    private Long version;

    @Column(name = "created",isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_OUTCOMES_V2, column = "created", fieldType = FieldType.LONG)
    private Long created;

    @Column(name = "modified",isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_OUTCOMES_V2, column = "modified", fieldType = FieldType.LONG)
    private Long modified;


    @Transient
    private String myVote = "0";
    @Transient
    private String vote = "0";
    @Transient
    private String handle = "";
    @Transient
    private String nickName = "";

    public BigDecimal getVolumeDecimal() {
        return StringUtils.isNotBlank(this.getVolume()) ? new BigDecimal(this.getVolume()) : BigDecimal.ZERO;
    }
}
