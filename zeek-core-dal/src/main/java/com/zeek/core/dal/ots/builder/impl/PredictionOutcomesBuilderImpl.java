package com.zeek.core.dal.ots.builder.impl;

import com.alicloud.openservices.tablestore.model.ColumnValue;
import com.alicloud.openservices.tablestore.model.Condition;
import com.alicloud.openservices.tablestore.model.Direction;
import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.condition.CompositeColumnValueCondition;
import com.alicloud.openservices.tablestore.model.condition.SingleColumnValueCondition;
import com.alicloud.openservices.tablestore.model.filter.ColumnValueFilter;
import com.alicloud.openservices.tablestore.model.filter.CompositeColumnValueFilter;
import com.alicloud.openservices.tablestore.model.filter.SingleColumnValueFilter;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.google.common.collect.Lists;
import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.framework.common.util.ChainIdUtil;
import com.kikitrade.framework.ots.RangeResult;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import com.zeek.core.common.constants.ZeekConstants;
import com.zeek.core.dal.ots.builder.PredictionOutcomesBuilder;
import com.zeek.core.dal.ots.model.PredictionOutcomesDO;
import com.zeek.core.dal.util.PrimaryKeyUtil;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: lizhifeng
 * @date: 2025/5/15 18:01
 */
@Repository
public class PredictionOutcomesBuilderImpl extends WideColumnStoreBuilder<PredictionOutcomesDO> implements PredictionOutcomesBuilder {

    @Override
    public String tableName() {
        return PredictionOutcomesDO.TABLE_NAME;
    }

    @PostConstruct
    public void init() {
        super.init(PredictionOutcomesDO.class);
    }

    @Override
    public boolean putPow(PredictionOutcomesDO predictionOutcomesDO) {
        return super.putRow(predictionOutcomesDO);
    }

    @Override
    public PredictionOutcomesDO getRow(String marketId, String customerId) {
        PredictionOutcomesDO predictionOutcomesDO = new PredictionOutcomesDO();
        predictionOutcomesDO.setMarketId(marketId);
        predictionOutcomesDO.setCustomerId(customerId);
        predictionOutcomesDO.setChainId(String.valueOf(ChainIdUtil.getChainId()));
        return getRow(predictionOutcomesDO);
    }

    @Override
    public PredictionOutcomesDO getRowBySlot(String marketId, Integer slot) {
        List<RangeQueryParameter> parameters = Lists.newArrayList();
        parameters.add(new RangeQueryParameter("market_id", PrimaryKeyValue.fromString(marketId), PrimaryKeyValue.fromString(marketId)));
        parameters.add(new RangeQueryParameter("customer_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        parameters.add(new RangeQueryParameter("chain_id", PrimaryKeyValue.fromString(String.valueOf(ChainIdUtil.getChainId())), PrimaryKeyValue.fromString(String.valueOf(ChainIdUtil.getChainId()))));

        CompositeColumnValueFilter filter = new CompositeColumnValueFilter(CompositeColumnValueFilter.LogicOperator.AND)
                .addFilter(new SingleColumnValueFilter("status", SingleColumnValueFilter.CompareOperator.NOT_EQUAL, ColumnValue.fromLong(ZeekConstants.OutcomeStatus.PENDING.getCode())))
                .addFilter(new SingleColumnValueFilter("slot", SingleColumnValueFilter.CompareOperator.EQUAL, ColumnValue.fromLong(slot)));

        List<PredictionOutcomesDO> list = rangeQueryWithNextToken(PredictionOutcomesDO.TABLE_NAME, parameters, filter, Direction.FORWARD, 1, null).list;
        return list != null && !list.isEmpty() ? list.get(0) : null;
    }

    @Override
    public List<PredictionOutcomesDO> getRows(String marketId, Integer status) {
        List<RangeQueryParameter> rangeQueryParameterList = Lists.newArrayList();
        rangeQueryParameterList.add(new RangeQueryParameter("market_id", PrimaryKeyValue.fromString(marketId), PrimaryKeyValue.fromString(marketId)));
        rangeQueryParameterList.add(new RangeQueryParameter("customer_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        rangeQueryParameterList.add(new RangeQueryParameter("chain_id", PrimaryKeyValue.fromString(String.valueOf(ChainIdUtil.getChainId())), PrimaryKeyValue.fromString(String.valueOf(ChainIdUtil.getChainId()))));
        ColumnValueFilter filter = null;
        if (status != null) {
            filter = new SingleColumnValueFilter("status", SingleColumnValueFilter.CompareOperator.EQUAL, ColumnValue.fromLong(status));
        }

        List<PredictionOutcomesDO> result = Lists.newArrayList();
        String nextToken = null;
        do {
            RangeResult<PredictionOutcomesDO> rangeResult = rangeQueryWithNextToken(PredictionOutcomesDO.TABLE_NAME, rangeQueryParameterList, filter, Direction.FORWARD, 100, PrimaryKeyUtil.parseNextToken(nextToken));
            nextToken = PrimaryKeyUtil.processNextToken(rangeResult.nextToken);
            result.addAll(rangeResult.list);
        } while (StringUtils.isNotBlank(nextToken));
        return result;
    }

    @Override
    public List<PredictionOutcomesDO> getEffectiveRows(String marketId) {
        List<RangeQueryParameter> rangeQueryParameterList = Lists.newArrayList();
        rangeQueryParameterList.add(new RangeQueryParameter("market_id", PrimaryKeyValue.fromString(marketId), PrimaryKeyValue.fromString(marketId)));
        rangeQueryParameterList.add(new RangeQueryParameter("customer_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX));
        rangeQueryParameterList.add(new RangeQueryParameter("chain_id", PrimaryKeyValue.fromString(String.valueOf(ChainIdUtil.getChainId())), PrimaryKeyValue.fromString(String.valueOf(ChainIdUtil.getChainId()))));
        ColumnValueFilter filter = null;
        filter = new SingleColumnValueFilter("status", SingleColumnValueFilter.CompareOperator.NOT_EQUAL, ColumnValue.fromLong(ZeekConstants.OutcomeStatus.PENDING.getCode()));


        List<PredictionOutcomesDO> result = Lists.newArrayList();
        String nextToken = null;
        do {
            RangeResult<PredictionOutcomesDO> rangeResult = rangeQueryWithNextToken(PredictionOutcomesDO.TABLE_NAME, rangeQueryParameterList, filter, Direction.FORWARD, 100, PrimaryKeyUtil.parseNextToken(nextToken));
            nextToken = PrimaryKeyUtil.processNextToken(rangeResult.nextToken);
            result.addAll(rangeResult.list);
        } while (StringUtils.isNotBlank(nextToken));
        return result;
    }

    @Override
    public List<PredictionOutcomesDO> getEffectiveRows(List<String> marketIds) {
        BoolQuery.Builder builder = QueryBuilders.bool();
        builder.must(QueryBuilders.term("chain_id", ChainIdUtil.getChainId()));
        if (!CollectionUtils.isEmpty(marketIds)) {
            builder.must(QueryBuilders.terms("market_id").terms(marketIds.toArray()));
        }
        builder.mustNot(QueryBuilders.term("status", ZeekConstants.OutcomeStatus.PENDING.getCode()));
        List<PredictionOutcomesDO> result = Lists.newArrayList();
        String nextToken="";
        do {
            TokenPage<PredictionOutcomesDO> tokenPage = super.pageSearchQuery(builder.build(), null, nextToken, 100, PredictionOutcomesDO.SEARCH_INDEX_PREDICTION_OUTCOMES_V2);
            result.addAll(tokenPage.getRows());
            nextToken = tokenPage.getNextToken();
        } while (StringUtils.isNotBlank(nextToken));
        return result;
    }

    @Override
    public boolean setStatusNormal(PredictionOutcomesDO outcomesDO) {
        return super.updateRow(outcomesDO, Lists.newArrayList("position_id", "slot", "status", "token", "value", "modified"), new Condition(RowExistenceExpectation.EXPECT_EXIST));
    }

    @Override
    public boolean setStatusPerfect(PredictionOutcomesDO outcomesDO) {
        outcomesDO.setStatus(ZeekConstants.OutcomeStatus.PERFECT.getCode());
        outcomesDO.setModified(System.currentTimeMillis());
        return super.updateRow(outcomesDO, Lists.newArrayList("status", "modified"), new Condition(RowExistenceExpectation.EXPECT_EXIST));
    }

    @Override
    public boolean incrementVolume(PredictionOutcomesDO outcomesDO, String value, String usdValue) {
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);

        CompositeColumnValueCondition columnValueCondition = new CompositeColumnValueCondition(CompositeColumnValueCondition.LogicOperator.AND);
        columnValueCondition.addCondition(new SingleColumnValueCondition("version", SingleColumnValueCondition.CompareOperator.EQUAL, ColumnValue.fromLong(outcomesDO.getVersion())));
        columnValueCondition.addCondition(new SingleColumnValueCondition("volume", SingleColumnValueCondition.CompareOperator.EQUAL, ColumnValue.fromString(outcomesDO.getVolume())));
        columnValueCondition.addCondition(new SingleColumnValueCondition("volume_usd", SingleColumnValueCondition.CompareOperator.EQUAL, ColumnValue.fromDouble(outcomesDO.getVolumeUsd())));
        condition.setColumnCondition(columnValueCondition);

        outcomesDO.setChainId(String.valueOf(ChainIdUtil.getChainId()));
        outcomesDO.setVersion(outcomesDO.getVersion() + 1);
        outcomesDO.setModified(System.currentTimeMillis());
        outcomesDO.setVolume(new BigDecimal(outcomesDO.getVolume()).add(new BigDecimal(value)).toString());
        outcomesDO.setVolumeUsd(BigDecimal.valueOf(outcomesDO.getVolumeUsd()).add(new BigDecimal(usdValue)).doubleValue());
        return updateRow(outcomesDO, List.of("volume", "volume_usd", "version", "modified"), condition);
    }

    @Override
    public boolean updateTotalPosition(PredictionOutcomesDO outcomesDO, String newTotalPosition, long eventTime) {
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);

        CompositeColumnValueCondition columnValueCondition = new CompositeColumnValueCondition(CompositeColumnValueCondition.LogicOperator.AND);
        columnValueCondition.addCondition(new SingleColumnValueCondition("slot", SingleColumnValueCondition.CompareOperator.EQUAL, ColumnValue.fromLong(outcomesDO.getSlot())));
        columnValueCondition.addCondition(new SingleColumnValueCondition("version", SingleColumnValueCondition.CompareOperator.EQUAL, ColumnValue.fromLong(outcomesDO.getVersion())));
        if (outcomesDO.getTotalPosition() != null) {
            columnValueCondition.addCondition(new SingleColumnValueCondition("total_position", SingleColumnValueCondition.CompareOperator.EQUAL, ColumnValue.fromString(outcomesDO.getTotalPosition())));
        }
        condition.setColumnCondition(columnValueCondition);

        outcomesDO.setVersion(outcomesDO.getVersion() + 1);
        outcomesDO.setModified(System.currentTimeMillis());
        outcomesDO.setTotalPosition(newTotalPosition);
        outcomesDO.setEventTime(eventTime);
        return updateRow(outcomesDO, List.of("total_position", "event_time", "version", "modified"), condition);
    }

}
