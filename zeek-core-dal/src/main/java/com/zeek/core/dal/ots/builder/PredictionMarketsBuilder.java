package com.zeek.core.dal.ots.builder;

import com.alicloud.openservices.tablestore.model.ColumnValue;
import com.alicloud.openservices.tablestore.model.Condition;
import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.condition.CompositeColumnValueCondition;
import com.alicloud.openservices.tablestore.model.condition.SingleColumnValueCondition;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.google.common.collect.Lists;
import com.kikitrade.framework.common.model.TokenPage;
import com.kikitrade.framework.common.util.ChainIdUtil;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import com.kikitrade.framework.ots.mapping.RangeQueryParameter;
import com.zeek.core.dal.ots.model.PredictionMarketsDO;
import com.zeek.core.common.constants.ZeekConstants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.List;


/**
 * <AUTHOR>
 * @description
 * @param[1] null
 * @date 2025/5/15 17:29
 **/
@Repository
public class PredictionMarketsBuilder extends WideColumnStoreBuilder<PredictionMarketsDO> {
    @PostConstruct
    public void init() {
        init(PredictionMarketsDO.class);
    }

    @Override
    public String tableName() {
        return PredictionMarketsDO.TABLE_NAME;
    }

    /**
     * 根据条件ID获取市场
     */
    public PredictionMarketsDO getRowByConditionId(String conditionId) {
        RangeQueryParameter conditionIdParameter = new RangeQueryParameter("condition_id", PrimaryKeyValue.fromString(conditionId), PrimaryKeyValue.fromString(conditionId));
        RangeQueryParameter chainIdParameter = new RangeQueryParameter("chain_id", PrimaryKeyValue.fromLong(ChainIdUtil.getChainId()), PrimaryKeyValue.fromLong(ChainIdUtil.getChainId()));
        RangeQueryParameter customerIdParameter = new RangeQueryParameter("customer_id", PrimaryKeyValue.INF_MIN, PrimaryKeyValue.INF_MAX);

        return rangeQueryOne(PredictionMarketsDO.TABLE_NAME, Lists.newArrayList(conditionIdParameter, chainIdParameter, customerIdParameter));
    }

    public TokenPage<PredictionMarketsDO> pageSearch(int limit, String nextToken, String status) {

        BoolQuery.Builder builder = QueryBuilders.bool();
        builder.filter(QueryBuilders.term("chain_id", ChainIdUtil.getChainId()));
        if (StringUtils.isNotBlank(status)) {
            builder.filter(QueryBuilders.terms("status").terms(Lists.newArrayList(status).toArray()));
        } else {
            builder.filter(QueryBuilders.terms("status").terms(Lists.newArrayList(ZeekConstants.MarketStatus.Running.name(),ZeekConstants.MarketStatus.Ended.name(),ZeekConstants.MarketStatus.Closed.name()).toArray()));
        }


        Sort sort = new Sort(List.of(
                // 发布时间：降序
                new FieldSort("status", SortOrder.DESC),
                new FieldSort("volume_sort", SortOrder.DESC),
                new FieldSort("endTime", SortOrder.ASC)

        ));
        return pageSearchQuery(builder.build(), sort, nextToken, limit, PredictionMarketsDO.SEARCH_INDEX_PREDICTION_MARKETS_V2);
    }

    public List<PredictionMarketsDO> getByMarketIds(List<String> marketIds) {
        BoolQuery.Builder builder = QueryBuilders.bool();
        builder.filter(QueryBuilders.term("chain_id", ChainIdUtil.getChainId()));
        builder.filter(QueryBuilders.terms("condition_id").terms(marketIds.toArray()));
        return search(builder.build(), null, 0, marketIds.size(), PredictionMarketsDO.SEARCH_INDEX_PREDICTION_MARKETS_V2);
    }

    public PredictionMarketsDO getByMarketAddress(String marketAddress) {
        BoolQuery.Builder builder = QueryBuilders.bool();
        builder.filter(QueryBuilders.term("chain_id", ChainIdUtil.getChainId()));
        builder.filter(QueryBuilders.term("market_address", marketAddress));
        return searchOne(builder.build(), PredictionMarketsDO.SEARCH_INDEX_PREDICTION_MARKETS_V2);
    }

    public boolean incrementVolume(PredictionMarketsDO marketsDO, String value, String usdValue, double valueSort) {
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);

        CompositeColumnValueCondition columnValueCondition = new CompositeColumnValueCondition(CompositeColumnValueCondition.LogicOperator.AND);
        columnValueCondition.addCondition(new SingleColumnValueCondition("version", SingleColumnValueCondition.CompareOperator.EQUAL, ColumnValue.fromLong(marketsDO.getVersion())));
        columnValueCondition.addCondition(new SingleColumnValueCondition("volume", SingleColumnValueCondition.CompareOperator.EQUAL, ColumnValue.fromString(marketsDO.getVolume())));
        columnValueCondition.addCondition(new SingleColumnValueCondition("volume_usd", SingleColumnValueCondition.CompareOperator.EQUAL, ColumnValue.fromDouble(marketsDO.getVolumeUsd())));
        condition.setColumnCondition(columnValueCondition);

        marketsDO.setChainId(ChainIdUtil.getChainId());
        marketsDO.setVersion(marketsDO.getVersion() + 1);
        marketsDO.setModified(System.currentTimeMillis());
        marketsDO.setVolume(new BigDecimal(marketsDO.getVolume()).add(new BigDecimal(value)).toString());
        marketsDO.setVolumeUsd(BigDecimal.valueOf(marketsDO.getVolumeUsd()).add(new BigDecimal(usdValue)).doubleValue());
        marketsDO.setVolumeSort(marketsDO.getVolumeSort() + valueSort);
        return updateRow(marketsDO, List.of("volume", "volume_usd", "volume_sort", "version", "modified"), condition);
    }

    public boolean updateFunding(PredictionMarketsDO marketsDO, String funding, long eventTime) {
        Condition condition = new Condition();
        condition.setRowExistenceExpectation(RowExistenceExpectation.EXPECT_EXIST);

        CompositeColumnValueCondition columnValueCondition = new CompositeColumnValueCondition(CompositeColumnValueCondition.LogicOperator.AND);
        columnValueCondition.addCondition(new SingleColumnValueCondition("condition_id", SingleColumnValueCondition.CompareOperator.EQUAL, ColumnValue.fromString(marketsDO.getConditionId())));
        columnValueCondition.addCondition(new SingleColumnValueCondition("version", SingleColumnValueCondition.CompareOperator.EQUAL, ColumnValue.fromLong(marketsDO.getVersion())));
        if (marketsDO.getFunding() != null) {
            columnValueCondition.addCondition(new SingleColumnValueCondition("funding", SingleColumnValueCondition.CompareOperator.EQUAL, ColumnValue.fromString(marketsDO.getFunding())));
        }
        condition.setColumnCondition(columnValueCondition);

        marketsDO.setVersion(marketsDO.getVersion() + 1);
        marketsDO.setModified(System.currentTimeMillis());
        marketsDO.setFunding(funding);
        marketsDO.setEventTime(eventTime);
        return updateRow(marketsDO, List.of("funding", "event_time", "version", "modified"), condition);
    }
}
