package com.zeek.core.dal.ots.model;

import com.alicloud.openservices.tablestore.model.search.FieldType;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.SearchIndex;
import com.kikitrade.framework.ots.annotations.Table;
import com.zeek.core.common.constants.ZeekConstants;
import lombok.Data;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.List;

/**
 * @author: xiang.bai
 * @description: 预测市场
 * @create: 2023-12-08
 **/
@Data
@Table(name = "prediction_markets")
public class PredictionMarketsDO implements Serializable {

    public static final String TABLE_NAME = "prediction_markets";
    public static final String SEARCH_INDEX_PREDICTION_MARKETS = "search_idx_prediction_markets";
    public static final String SEARCH_INDEX_PREDICTION_MARKETS_V2 = "search_idx_prediction_markets_v2";

    @PartitionKey(name = "condition_id", value = 0)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "condition_id", fieldType = FieldType.KEYWORD)
    private String conditionId;

    @PartitionKey(name = "chain_id", value = 1)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "chain_id", fieldType = FieldType.LONG)
    private Long chainId;

    @PartitionKey(name = "customer_id", value = 2)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "customer_id", fieldType = FieldType.KEYWORD)
    private String customerId;

    @Column(name = "question_id", isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "question_id", fieldType = FieldType.KEYWORD)
    private String questionId;

    @Column(name = "market_address", isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "market_address", fieldType = FieldType.KEYWORD)
    private String marketAddress;

    @Column(name = "title", isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "title", fieldType = FieldType.KEYWORD)
    private String title;

    @Column(name = "content", isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "content", fieldType = FieldType.KEYWORD)
    private String content;

    @Column(name = "medias", isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "medias", fieldType = FieldType.KEYWORD)
    private List<String> medias;

    @Column(name = "token", isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "token", fieldType = FieldType.KEYWORD)
    private String token;

    @Column(name = "status", isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "status", fieldType = FieldType.KEYWORD)
    private String status;

    @Column(name = "value", isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "value", fieldType = FieldType.KEYWORD)
    private String value;

    @Column(name = "slot", isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "slot", fieldType = FieldType.LONG)
    private Long slot;

    @Column(name = "volume", isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "volume", fieldType = FieldType.KEYWORD)
    private String volume;

    @Column(name = "volume_usd", isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "volume_usd", fieldType = FieldType.DOUBLE)
    private Double volumeUsd;

    @Column(name = "volume_sort", isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "volume_sort", fieldType = FieldType.DOUBLE)
    private Double volumeSort;

    @Column(name = "vote", isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "vote", fieldType = FieldType.LONG)
    private Long vote;

    @Column(name = "fee", isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "fee")
    private String fee;

    @Column(name = "creator_fee", isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "creator_fee")
    private String creatorFee;

    @Column(name = "proposal_fee", isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "proposal_fee")
    private String proposalFee;

    @Column(name = "admin_address",isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "admin_address")
    private String adminAddress;

    @Column(name = "finance_address",isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "finance_address")
    private String financeAddress;

    @Column(name = "oracle_address",isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "oracle_address")
    private String oracleAddress;

    /**
     * @see ZeekConstants.PredictionOracleType
     */
    @Column(name = "oracle_type",isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "oracle_type", fieldType = FieldType.KEYWORD)
    private String oracleType;

    @Column(name = "funding",isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "funding", fieldType = FieldType.KEYWORD)
    private String funding;

    @Column(name = "event_time",isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "event_time", fieldType = FieldType.LONG)
    private Long eventTime;

    @Column(name = "endTime", isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "endTime", fieldType = FieldType.LONG)
    private Long endTime;

    @Column(name = "version",isDefined = true)
    private Long version;

    @Column(name = "created", isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "created", fieldType = FieldType.LONG)
    private Long created;

    @Column(name = "modified",isDefined = true)
    @SearchIndex(name = SEARCH_INDEX_PREDICTION_MARKETS_V2, column = "modified", fieldType = FieldType.LONG)
    private Long modified;

    public BigInteger getSlotUnit() {
        return new BigInteger(this.getValue()).divide(BigInteger.valueOf(this.getSlot()));
    }

}
